import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:talker_flutter/talker_flutter.dart';

import '../../enums.dart';

class MessageProvider {
  final WabowProvider wabowProvider;
  Talker get talker => wabowProvider.talker;

  MessageProvider({
    required this.wabowProvider,
  });

  Future<void> mark<PERSON><PERSON>ead(MessageType messageType) async {
    try {
      await wabowProvider.putMembersMessagesRead(messageType);
      await wabowProvider.getMembersMessagesUnread();
    } catch (e) {
      talker.error(e);
    }
  }
}
