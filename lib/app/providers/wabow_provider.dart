import 'dart:convert';

import 'package:appier_flutter/appier_flutter.dart';
import 'package:dio/dio.dart';
import 'package:efshop/app/models/appier_user_login_req.dart';
import 'package:efshop/app/providers/api_provider.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter_app_badger/flutter_app_badger.dart';
import 'package:get_storage/get_storage.dart';

import 'package:talker_flutter/talker_flutter.dart';

import '../../error_type.dart';
import '../models/appier_product_removed_from_cart_res.dart';
import '../models/appier_registration_completed_req.dart';
import '../models/appier_searched_req.dart';
import '../models/cart_add_on_post_req.dart';
import '../models/cart_items_res.dart';
import '../models/cart_quantity_res.dart';
import '../models/categories_app.dart';
import '../models/categories_id_quantity_req.dart';
import '../models/categories_id_req.dart';
import '../models/categories_seo_res.dart';
import '../models/category.dart';
import '../models/checkout_post_req.dart';
import '../models/checkout_post_res.dart';
import '../models/checkout_res.dart';
import '../models/city.dart';
import '../models/configs_res.dart';
import '../models/content_res.dart';
import '../models/devices_create_res.dart';
import '../models/error_res.dart';
import '../models/faq_res.dart';
import '../models/index_module_app_res.dart';
import '../models/index_res.dart';
import '../models/line_token_req.dart';
import '../models/line_token_res.dart';
import '../models/login_apple_req.dart';
import '../models/login_req.dart';
import '../models/login_res.dart';
import '../models/logout_res.dart';
import '../models/members_address_res.dart';
import '../models/members_addresses_post_req.dart';
import '../models/members_feedbacks_req.dart';
import '../models/members_messages_activity.dart';
import '../models/members_messages_order.dart';
import '../models/members_messages_preference_res.dart';
import '../models/members_messages_question.dart';
import '../models/members_messages_ship.dart';
import '../models/members_messages_summary.dart';
import '../models/members_messages_unread_res.dart';
import '../models/members_my_bonus_res.dart';
import '../models/members_my_favorite_post_req.dart';
import '../models/members_my_favorite_put_res.dart';
import '../models/members_my_favorite_res.dart';
import '../models/members_orders_comment.dart';
import '../models/members_orders_comments_post_req.dart';
import '../models/members_orders_questions_post_req.dart';
import '../models/members_orders_questions_res.dart';
import '../models/members_orders_refund_res.dart';
import '../models/members_orders_message.dart';
import '../models/members_orders_invoices_res.dart';
import '../models/members_orders_refund_post_req.dart';
import '../models/members_orders_res.dart';
import '../models/members_password_patch_req.dart';
import '../models/members_popup_res.dart';
import '../models/members_preorders_res.dart';
import '../models/members_profile_patch_req.dart';
import '../models/members_profile_patch_res.dart';
import '../models/members_profile_res.dart';
import '../models/members_refund_post_req.dart';
import '../models/members_refund_res.dart';
import '../models/menu_res.dart';
import '../models/message_res.dart';
import '../models/payment_options_res.dart';
import '../models/product.dart';
import '../models/product_added_to_cart_req.dart';
import '../models/product_detail.dart';
import '../models/product_series_res.dart';
import '../models/products_collocation_res.dart';
import '../models/products_id_comments_res.dart';
import '../models/products_preorder_post_res.dart';
import '../models/promotion_detail_res.dart';
import '../models/promotion_id_req.dart';
import '../models/promotion_res.dart';
import '../models/promotions_add_on_discount_res.dart';
import '../models/promotions_add_on_ids_res.dart';
import '../models/register_apple_req.dart';
import '../models/register_line_req.dart';
import '../models/register_req.dart';
import '../models/store_detail.dart';
import '../models/voucher_res.dart';
import 'appier_provider.dart';
import 'box_provider.dart';

class WabowProvider {
  final ApiProvider apiProvider;
  final AppierProvider appierProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  BoxProvider get boxProvider => prefProvider.boxProvider;
  Talker get talker => apiProvider.talker;

  WabowProvider({
    required this.apiProvider,
    required this.appierProvider,
  });

  ///
  /// 登入
  ///
  Future<LoginRes> login(LoginReq loginReq) async {
    final res = await apiProvider.post(
      'login',
      (json) => LoginRes.fromJson(json!),
      data: loginReq.toJson(),
    );
    // 儲存
    prefProvider.loginRes = res;
    // appier
    final jwt = res.jwtRes;
    try {
      await appierProvider.userLogin(AppierUserLoginReq(
        email: jwt?.email,
        userId: jwt?.userid,
        points: jwt?.bonus,
        userName: jwt?.fullname,
        gender: jwt?.gender,
        birthday: jwt?.birthday,
        profileUpdateDate: DateTime.now().dateAsInt,
      ));
    } catch (e, s) {
      talker.handle(e, s, '[Appier] userLogin failed');
    }
    return res;
  }

  ///
  /// 更新 token
  ///
  Future<LoginRes> renew() async {
    final res = await apiProvider.post(
        'login/renew', (json) => LoginRes.fromJson(json!));
    // 儲存
    prefProvider.loginRes = res;
    return res;
  }

  ///
  /// 登出
  ///
  Future<LogoutRes> logout() {
    try {
      return apiProvider.post('logout', (json) => LogoutRes.fromJson(json!));
    } finally {
      // 移除 token
      prefProvider.loginRes = null;
    }
  }

  ///
  /// 檢查 Email 註冊狀況
  ///
  Future<bool> registerCheck(String email) async {
    try {
      final res = await apiProvider.post(
        'register/check',
        (json) => json?[Keys.status] ?? false,
        data: {
          'email': email,
        },
      );
      return res;
    } on ErrorRes catch (e, s) {
      talker.handle(e, s, '[API] register/check failed');
      return false;
      // } catch (e) {
      //   // 因找不到 email 返回 status = false，會拋出例外
      //   // 所以 cache 到後返回 false
      //   return false;
    }
  }

  ///
  /// 本站註冊使用者
  ///
  Future<LoginRes> register(RegisterReq registerReq) async {
    final res = await apiProvider.post(
      'register',
      (json) => LoginRes.fromJson(json!),
      data: registerReq.toJson(),
    );
    // 儲存
    prefProvider.loginRes = res;
    // appier event
    final jwt = res.jwtRes;
    try {
      await appierProvider.registrationCompleted(AppierRegistrationCompletedReq(
        email: jwt?.email,
        userId: jwt?.userid,
        points: jwt?.bonus,
        userName: jwt?.fullname,
        gender: jwt?.gender,
        birthday: jwt?.birthday,
        profileUpdateDate: DateTime.now().dateAsInt,
      ));
    } catch (e, s) {
      talker.handle(e, s, '[Appier] registrationCompleted failed');
    }
    return res;
  }

  ///
  /// 忘記密碼處理
  ///
  Future<MessageRes> forgetPassword(String email) async {
    final res = await apiProvider.getResData(
      'login/reset/password',
      (json) => MessageRes.fromJson(json),
      filter: {
        'email': email,
      },
    );
    return res;
  }

  ///
  /// Facebook 使用者登入
  ///
  Future<LoginRes> loginFacebook(String accessToken) async {
    final res = await apiProvider.post(
      'login/facebook',
      (json) => LoginRes.fromJson(json!),
      data: {
        'access_token': accessToken,
      },
    );
    // 儲存
    prefProvider.loginRes = res;
    // appier
    final jwt = res.jwtRes;
    try {
      await appierProvider.userLogin(AppierUserLoginReq(
        email: jwt?.email,
        userId: jwt?.userid,
        points: jwt?.bonus,
        userName: jwt?.fullname,
        gender: jwt?.gender,
        birthday: jwt?.birthday,
        profileUpdateDate: DateTime.now().dateAsInt,
      ));
    } catch (e, s) {
      talker.handle(e, s, '[Appier] userLogin failed');
    }
    return res;
  }

  ///
  /// Line 使用者登入
  ///
  Future<LoginRes> loginLine(String accessToken) async {
    final res = await apiProvider.post(
      'login/line',
      (json) {
        talker.debug('json: $json');
        return LoginRes.fromJson(json!);
      },
      data: {
        'access_token': accessToken,
      },
    );
    // 儲存
    prefProvider.loginRes = res;
    // appier
    final jwt = res.jwtRes;
    try {
      await appierProvider.userLogin(AppierUserLoginReq(
        email: jwt?.email,
        userId: jwt?.userid,
        points: jwt?.bonus,
        userName: jwt?.fullname,
        gender: jwt?.gender,
        birthday: jwt?.birthday,
        profileUpdateDate: DateTime.now().dateAsInt,
      ));
    } catch (e, s) {
      talker.handle(e, s, '[Appier] userLogin failed');
    }
    return res;
  }

  ///
  /// Apple 使用者登入
  ///
  Future<LoginRes> loginApple(LoginAppleReq loginAppleReq) async {
    final res = await apiProvider.post(
      'login/apple',
      (json) => LoginRes.fromJson(json!),
      data: loginAppleReq.toJson(),
    );
    // 儲存
    prefProvider.loginRes = res;
    // appier
    final jwt = res.jwtRes;
    try {
      await appierProvider.userLogin(AppierUserLoginReq(
        email: jwt?.email,
        userId: jwt?.userid,
        points: jwt?.bonus,
        userName: jwt?.fullname,
        gender: jwt?.gender,
        birthday: jwt?.birthday,
        profileUpdateDate: DateTime.now().dateAsInt,
      ));
    } catch (e, s) {
      talker.handle(e, s, '[Appier] userLogin failed');
    }
    return res;
  }

  ///
  /// 忘記密碼處理 (用途不明) (沒使用到)
  ///
  Future<LoginRes> forgetPasswordX(String userId, String forgetKey) async {
    final res = await apiProvider.getResData(
      'login/reset/password/$userId/$forgetKey',
      (json) => LoginRes.fromJson(json!),
    );
    // 儲存
    prefProvider.loginRes = res;
    return res;
  }

  ///
  /// 取得 session token (用途不明) (沒使用到)
  ///
  Future<LoginRes> loginSession() async {
    final res = await apiProvider.getResData(
      'login/session',
      (json) => LoginRes.fromJson(json!),
    );
    // 儲存
    prefProvider.loginRes = res;
    return res;
  }

  ///
  /// 本站註冊使用者 (綁定 Line mid)
  ///
  Future<LoginRes> registerLine(RegisterLineReq registerLineReq) async {
    final res = await apiProvider.post(
      'register/line',
      (json) => LoginRes.fromJson(json!),
      data: registerLineReq.toJson(),
    );
    // 儲存
    prefProvider.loginRes = res;
    // appier
    final jwt = res.jwtRes;
    try {
      await appierProvider.userLogin(AppierUserLoginReq(
        email: jwt?.email,
        userId: jwt?.userid,
        points: jwt?.bonus,
        userName: jwt?.fullname,
        gender: jwt?.gender,
        birthday: jwt?.birthday,
        profileUpdateDate: DateTime.now().dateAsInt,
      ));
    } catch (e, s) {
      talker.handle(e, s, '[Appier] userLogin failed');
    }
    return res;
  }

  ///
  /// 本站註冊使用者 (綁定 Apple uid) (沒使用到)
  ///
  Future<LoginRes> registerApple(RegisterAppleReq registerAppleReq) async {
    final json = registerAppleReq.toJson();
    if (registerAppleReq.password == null ||
        registerAppleReq.password!.isEmpty) {
      json.remove('password');
    }
    final res = await apiProvider.post(
      'register/apple',
      (json) => LoginRes.fromJson(json!),
      data: json,
    );
    // 儲存
    prefProvider.loginRes = res;
    // appier event
    final jwt = res.jwtRes;
    try {
      await appierProvider.registrationCompleted(AppierRegistrationCompletedReq(
        email: jwt?.email,
        userId: jwt?.userid,
        points: jwt?.bonus,
        userName: jwt?.fullname,
        gender: jwt?.gender,
        birthday: jwt?.birthday,
        profileUpdateDate: DateTime.now().dateAsInt,
      ));
    } catch (e, s) {
      talker.handle(e, s, '[Appier] registrationCompleted failed');
    }
    return res;
  }

  ///
  /// 取得有 7-11 的所有縣市資料
  ///
  Future<Map<String, dynamic>> getStoreSevenElevenCities() {
    return apiProvider.getResData(
      'store/seven_eleven/cities',
      (json) => json,
    );
  }

  ///
  /// 取得有 7-11 的所有鄉鎮資料
  ///
  Future<Iterable<City>> getStoreSevenElevenTowns() {
    return apiProvider.getResData(
      'store/seven_eleven/towns',
      (json) => List.from(json ?? []).map((e) => City.fromJson(e)),
    );
  }

  ///
  /// 取得某區域的所有 7-11 店家資料
  ///
  Future<Map<String, Iterable<StoreDetail>>> getStoreSevenElevenWithZip(
      String zip) {
    return apiProvider.getResData(
      'store/seven_eleven/zip_code/$zip',
      (json) {
        return Map.from(json ?? {}).map((key, value) {
          final it =
              Iterable.castFrom(value).map((e) => StoreDetail.fromJson(e));
          return MapEntry(key, it);
        });
      },
    );
  }

  ///
  /// 取得有全家的所有縣市資料
  ///
  Future<Map<String, dynamic>> getStoreFamilyMartCities() {
    return apiProvider.getResData(
      'store/family_mart/cities',
      (json) => json ?? {},
    );
  }

  ///
  /// 取得有全家的所有鄉鎮資料
  ///
  Future<Iterable<City>> getStoreFamilyMartTowns() {
    return apiProvider.getResData(
      'store/family_mart/towns',
      (json) => List.from(json ?? []).map((e) => City.fromJson(e)),
    );
  }

  ///
  /// 取得某區域的所有全家店家資料
  ///
  Future<Map<String, Iterable<StoreDetail>>> getStoreFamilyMartWithZip(
      String zip) {
    return apiProvider.getResData(
      'store/family_mart/zip_code/$zip',
      (json) {
        return Map.from(json ?? {}).map((key, value) {
          final it =
              Iterable.castFrom(value).map((e) => StoreDetail.fromJson(e));
          return MapEntry(key, it);
        });
      },
    );
  }

  ///
  /// 產品搜尋
  ///
  Future<Iterable<ProductDetail>> getSearchProducts(String keyword) async {
    final res = await apiProvider.getResData(
      'search/products/$keyword',
      (json) => List.from(json ?? []).map((e) {
        var item = ProductDetail.fromJson(e);
        // fix id
        if (item.id == null || item.id!.isEmpty || item.id == 'null') {
          item.id = item.url?.id;
        }
        return item;
      }),
    );
    // send appier search event
    try {
      await appierProvider.searched(AppierSearchedReq(
        searchedKeyword: keyword,
      ));
    } catch (e, s) {
      talker.handle(e, s, '[Appier] searched failed');
    }
    return res;
  }

  ///
  /// 產品標籤搜尋
  ///
  Future<Iterable<ProductDetail>> getTagProducts(String keyword) {
    return apiProvider.getResData(
      'tags/products/$keyword',
      (json) => List.from(json ?? []).map((e) => ProductDetail.fromJson(e)),
    );
  }

  ///
  /// 熱門關鍵字-筆數由後台設定
  ///
  Future<Iterable<Category>> getHotKeyword() {
    return apiProvider.getResData(
      'hot_terms',
      (json) => List.from(json ?? []).map((e) => Category.fromJson(e)),
    );
  }

  ///
  /// 首頁廣告 - index
  ///
  Future<IndexRes> getIndex() {
    return apiProvider.getResData(
      'index',
      (json) => IndexRes.fromJson(json),
    );
  }

  ///
  /// 首頁廣告 - index mobile
  ///
  Future<IndexRes> getIndexMobile() async {
    try {
      apiProvider.httpClient.options.headers['X-Client-Agent'] = 'web-app';
      final res = await apiProvider.getResData(
        'index/mobile',
        (json) => IndexRes.fromJson(json),
      );
      return res;
    } finally {
      apiProvider.httpClient.options.headers.remove('X-Client-Agent');
    }
  }

  ///
  /// 首頁廣告中 - 期間限定資料
  ///
  Future<Iterable<Category>> getPromotionsLimited() {
    return apiProvider.getResData(
      'promotions/limited',
      (json) => List.from(json ?? []).map((e) => Category.fromJson(e)),
    );
  }

  ///
  /// 首頁廣告中 - 期間限定資料(含任選與買n送m)
  ///
  Future<Iterable<Category>> getPromotionsLimitedNMGiftMix() {
    return apiProvider.getResData(
      'promotions/limited-nmgiftmix',
      (json) => List.from(json ?? []).map((e) => Category.fromJson(e)),
    );
  }

  ///
  /// 首頁模組
  ///
  Future<Map<String, IndexModuleAppRes>> getIndexModuleApp() {
    return apiProvider.getResData('index/module/app', (json) {
      final jsonIsMap = json is Map;
      return Map.from(jsonIsMap ? json : {})
          .map((k, v) => MapEntry(k, IndexModuleAppRes.fromJson(v)));
    });
  }

  ///
  /// 分類廣告
  ///
  Future<Iterable<Category>> getAdvsCategories() {
    return apiProvider.getResData(
      'advs/categories',
      (json) => List.from(json ?? []).map((e) => Category.fromJson(e)),
    );
  }

  ///
  /// 靜態頁面 - index
  ///
  Future<ContentRes> getContent(String id) {
    return apiProvider.getResData(
      '/content/$id',
      (json) => ContentRes.fromJson(json ?? {}),
    );
  }

  ///
  /// 搜尋欄關聯字詞
  ///
  Future<Iterable<String>> getSuggestions() {
    return apiProvider.getResData(
      'suggestions',
      (json) => List.from(json ?? []),
    );
  }

  ///
  /// 取得巢狀的兩層類別列表
  ///
  Future<Iterable<Category>> getCategories() {
    return apiProvider.getResData(
      'categories',
      (json) => List.from(json ?? []).map((e) => Category.fromJson(e)),
    );
  }

  ///
  /// 取得新版巢狀類別列表
  ///
  Future<Iterable<Category>> getCategoriesIndex() async {
    final res = await apiProvider.getResData(
      'categories/index',
      (json) => List.from(json ?? []).map((e) => Category.fromJson(e)),
    );
    await _saveCategoriesToBox(res);
    return res;
  }

  Future<void> _saveCategoriesToBox(Iterable<Category> res) async {
    final box = boxProvider.getGsBox(Boxes.category.name);
    await box.erase();
    for (final item in res) {
      item.updateChildren();
      await _saveCategories(item, box);
    }
  }

  ///
  /// 儲存類別資料
  ///
  Future<void> _saveCategories(Category category, GetStorage box) async {
    await box.write(category.uniqueId, category.toJson());
    for (final child in category.subCategories ?? []) {
      await _saveCategories(child, box);
    }
  }

  ///
  /// 取得單一類別的所有產品 (沒有使用)
  ///
  Future<Iterable<ProductDetail>> getCategoriesWithId(
      String id, CategoriesIdReq query) {
    return apiProvider.getResData(
      'categories/$id',
      (json) => List.from(json ?? []).map((e) => ProductDetail.fromJson(e)),
      filter: query.toJson(),
    );
  }

  ///
  /// 取得單一類別的所有產品 (沒有使用)
  ///
  Future<Iterable<ProductDetail>> getCategoriesWithIdAndQuantity(
      String id, int quantity, CategoriesIdQuantityReq query) {
    return apiProvider.getResData(
      'categories/$id/$quantity',
      (json) => List.from(json ?? []).map((e) => ProductDetail.fromJson(e)),
      filter: query.toJson(),
    );
  }

  ///
  /// 取得單一類別的 seo
  ///
  Future<CategoriesSeoRes> getCategoriesSeo(String id) {
    return apiProvider.getResData(
      'categories/$id/seo',
      (json) => CategoriesSeoRes.fromJson(json ?? {}),
    );
  }

  ///
  /// 取得類別商品
  ///
  Future<Iterable<CategoriesApp>> getCategoriesAppWithIdAndQuantity(
      String id, int quantity, CategoriesIdQuantityReq query) {
    return apiProvider.getResData(
      'categories/app/$id/$quantity',
      (json) => List.from(json ?? []).map((e) => CategoriesApp.fromJson(e)),
      filter: query.toJson(),
    );
  }

  ///
  /// 取得單一產品資料
  ///
  Future<ProductDetail> getProducts(String id) async {
    final res = await apiProvider.getResData(
      'products/$id',
      (json) => ProductDetail.fromJson(json ?? {}),
    );
    final box = boxProvider.getGsBox(Boxes.productDetail.name);
    box.write(id, res.toJson());
    return res;
  }

  ///
  /// 取得某產品的所有說明圖片或影片
  ///
  Future<Iterable<String>> getProductsGroupImages(String id) {
    return apiProvider.getResData(
      'products/$id/group_images',
      (json) => List.from(json ?? []).map((e) => '$e'),
    );
  }

  ///
  /// 取得某產品的同系列產品資料
  ///
  Future<Iterable<ProductSeriesRes>> getProductsGroup(String id) async {
    final res = await apiProvider.getResData(
      'products/$id/group',
      (json) => List.from(json ?? []).map((e) => ProductSeriesRes.fromJson(e)),
    );
    final box = boxProvider.getGsBox(Boxes.productSeries.name);
    final it = res.map((e) => e.toJson());
    box.write(id, [...it]);
    return res;
  }

  ///
  /// 取得某產品的評價資料
  ///
  Future<Iterable<ProductsCommentsRes>> getProductsComments(String id) async {
    final res = await apiProvider.getResData(
      'products/$id/comments',
      (json) =>
          List.from(json ?? []).map((e) => ProductsCommentsRes.fromJson(e)),
    );
    final box = boxProvider.getGsBox(Boxes.productComment.name);
    final it = res.map((e) => e.toJson());
    box.write(id, [...it]);
    return res;
  }

  ///
  /// 取得某產品的搭配購產品資料
  ///
  Future<Iterable<ProductsCollocationRes>> getProductsCollocation(String id) {
    return apiProvider.getResData(
      'products/$id/collocation',
      (json) =>
          List.from(json ?? []).map((e) => ProductsCollocationRes.fromJson(e)),
    );
  }

  ///
  /// 取得同質產品的搭配購產品資料
  ///
  Future<Map<String, ProductsCollocationRes>> getProductsCollocationAll(
      String id) {
    return apiProvider.getResData(
      'products/$id/collocation/all',
      (json) => Map.from(json ?? {})
          .map((k, v) => MapEntry(k, ProductsCollocationRes.fromJson(v))),
    );
  }

  ///
  /// 新增商品到購物車
  ///
  Future<MessageRes> postCart(String productId, int quantity) async {
    var res = MessageRes(status: true);
    try {
      if (prefProvider.isNotLogin) {
        throw ErrorType.unauthorized;
      }
      res = await apiProvider.post(
        'cart/$productId/$quantity',
        (json) => MessageRes.fromJson(json ?? {}),
      );
    } catch (e) {
      await _postCart(productId, quantity);
      // 重新發送購物車商品事件，用來刷新商品狀態
      prefProvider.refreshCartItems();
    }

    /// 重新取得購物車商品數量
    await fetchCartQuantity();
    // appier event
    final product = await getProducts(productId);
    await _appierProductAddedToCart(product);
    // GA event
    _logAddToCart(product, quantity);

    return res;
  }

  ///
  /// 未登入: 新增商品到本地購物車
  ///
  Future<void> _postCart(String productId, int quantity) async {
    // add to box
    final box = boxProvider.getGsBox(Boxes.cart.name, withNamespace: false);
    final key = productId;
    final value = box.read(key) ?? 0;
    await box.write(key, value + quantity);
  }

  ///
  /// appier event: log add to cart
  ///
  Future<void> _appierProductAddedToCart(ProductDetail product) async {
    try {
      await appierProvider.productAddedToCart(ProductAddedToCartReq(
        categoryName: product.topCategoryName,
        typeName: product.categoryName,
        productId: product.id,
        productName: product.name,
        productImageUrl: product.thumbnail?.src,
        productUrl: product.shareUrl,
        productPrice: product.finalPrice,
      ));
    } catch (e, s) {
      talker.handle(e, s, '[Appier] productAddedToCart failed');
    }
  }

  ///
  /// GA event: add_to_cart
  ///
  Future<void> _logAddToCart(ProductDetail product, int quantity) async {
    try {
      await FirebaseAnalytics.instance.logAddToCart(
        items: [product.toAnalyticsEventItem(quantity: quantity)],
        value: double.tryParse(product.price ?? ''),
        currency: 'TWD',
      );
    } catch (e, s) {
      talker.handle(e, s, '[GA] logAddToCart failed');
    }
  }

  Future<void> fetchCartQuantity() async {
    try {
      if (prefProvider.isNotLogin) {
        throw ErrorType.unauthorized;
      }
      final res = await getCartQuantity();
      prefProvider.cartQuantity = res.quantity ?? 0;
    } catch (e, s) {
      talker.error('$e', e, s);
      prefProvider.cartQuantity = _getCartQuantity();
    }
    // get cart items
    try {
      prefProvider.cartItems = await getCartItems();
    } catch (e, s) {
      talker.error('$e', e, s);
    }
  }

  int _getCartQuantity() {
    final box = boxProvider.getGsBox(Boxes.cart.name, withNamespace: false);
    final keys = List<String>.from(box.getKeys());
    return keys.fold<int>(0, (previousValue, element) {
      final value = box.read(element);
      return previousValue + (value is int ? value : 0);
    });
  }

  ///
  /// 更新商品到購物車 (沒使用到)
  /// 需要重新取得購物車商品數量
  ///
  Future<MessageRes> patchCart(String productId, int quantity) async {
    final res = await apiProvider.patch(
      'cart/$productId/$quantity',
      (json) => MessageRes.fromJson(json ?? {}),
    );
    await fetchCartQuantity();
    // appier event
    try {
      final product = await getProducts(productId);
      await appierProvider.productAddedToCart(ProductAddedToCartReq(
        categoryName: product.topCategoryName,
        typeName: product.categoryName,
        productId: product.id,
        productName: product.name,
        productImageUrl: product.thumbnail?.src,
        productUrl: product.shareUrl,
        productPrice: product.finalPrice,
      ));
    } catch (e, s) {
      talker.handle(e, s, '[Appier] productAddedToCart failed');
    }
    return res;
  }

  ///
  /// 點重新購買時，購物車內若已有相同項目則直接取代，確保與訂單項目相同，不累計；也不刪除其他品項
  /// 需要重新取得購物車商品數量
  ///
  Future<MessageRes> postCartBuyAgain(String orderId) async {
    final res = await apiProvider.post(
      'cart/buy_again/$orderId',
      (json) => MessageRes.fromJson(json ?? {}),
    );
    await fetchCartQuantity();
    // appier event
    try {
      // get order detail
      final order = await getMembersOrdersWithId(orderId);
      for (final product in order.products ?? <Product>[]) {
        try {
          await appierProvider.productAddedToCart(ProductAddedToCartReq(
            // categoryName: product.categoryName,
            // typeName: product.typeName,
            productId: product.productId,
            productName: product.productName,
            productImageUrl: product.thumbnail?.src,
            productUrl: product.shareUrl,
            productPrice: product.subtotal,
          ));
        } catch (e, s) {
          talker.handle(e, s, '[Appier] productAddedToCart failed');
        }
      }
    } catch (e, s) {
      talker.handle(e, s, '[Appier] productAddedToCart failed');
    }
    return res;
  }

  ///
  /// 刪除購物車商品 (沒使用到)
  /// 需要重新取得購物車商品數量
  ///
  Future<MessageRes> deleteCart(String productId) async {
    final res = await apiProvider.delete(
      'cart/$productId',
      (json) => MessageRes.fromJson(json ?? {}),
    );
    await fetchCartQuantity();
    try {
      final product = await getProducts(productId);
      await appierProvider
          .productRemovedFromCart(AppierProductRemovedFromCartRes(
        categoryName: product.topCategoryName,
        typeName: product.categoryName,
        productId: product.id,
        productName: product.name,
        productImageUrl: product.thumbnail?.src,
        productUrl: product.shareUrl,
        productPrice: product.finalPrice,
      ));
    } catch (e, s) {
      talker.handle(e, s, '[Appier] productRemovedFromCart failed');
    }
    return res;
  }

  ///
  /// 新增多筆加價購商品到購物車 (沒使用到)
  /// 需要重新取得購物車商品數量
  ///
  Future<MessageRes> postCartAddOn(Iterable<CartAddOnPostReq> items) async {
    final res = await apiProvider.post(
      'cart/add_on',
      (json) => MessageRes.fromJson(json ?? {}),
      data: {
        'items': jsonEncode(items.toList(growable: false)),
      },
    );
    await fetchCartQuantity();
    // appier event
    try {
      for (var element in items) {
        final product = await getProducts(element.productId ?? '');
        await appierProvider.productAddedToCart(ProductAddedToCartReq(
          categoryName: product.topCategoryName,
          typeName: product.categoryName,
          productId: product.id,
          productName: product.name,
          productImageUrl: product.thumbnail?.src,
          productUrl: product.shareUrl,
          productPrice: product.finalPrice,
        ));
      }
    } catch (e, s) {
      talker.handle(e, s, '[Appier] productAddedToCart failed');
    }
    return res;
  }

  ///
  /// 清空購物車同優惠商品，並新增買n送m小購物車商品到購物車 (沒使用到)
  ///
  Future<MessageRes> postCartBuyNGiftM(
      int promotionId, Iterable<CartAddOnPostReq> items) async {
    final res = await apiProvider.post(
      'cart/buy_n_gift_m/$promotionId',
      (json) => MessageRes.fromJson(json ?? {}),
      data: {
        'items': jsonEncode(items.toList(growable: false)),
      },
    );
    await fetchCartQuantity();
    // appier event
    try {
      for (var element in items) {
        final product = await getProducts(element.productId ?? '');
        await appierProvider.productAddedToCart(ProductAddedToCartReq(
          categoryName: product.topCategoryName,
          typeName: product.categoryName,
          productId: product.id,
          productName: product.name,
          productImageUrl: product.thumbnail?.src,
          productUrl: product.shareUrl,
          productPrice: product.finalPrice,
        ));
      }
    } catch (e, s) {
      talker.handle(e, s, '[Appier] productAddedToCart failed');
    }
    return res;
  }

  ///
  /// 查看購物車商品數量
  ///
  Future<CartQuantityRes> getCartQuantity() {
    return apiProvider.getResData(
      'cart/quantity',
      (json) => CartQuantityRes.fromJson(json ?? {}),
    );
  }

  ///
  /// cart 與 checkout 頁面取得購物車包含商品與總價等計算的詳細資訊
  ///
  Future<CartItemsRes> getCartItems() {
    return apiProvider.getResData(
      'cart/items',
      (json) => CartItemsRes.fromJson(json ?? {}),
    );
  }

  ///
  /// 設定本次結帳使用紅利點數 (沒使用到)
  ///
  Future<MessageRes> postCartSetBonusPoints(int points) {
    return apiProvider.post(
      'cart/set/bonus/$points',
      (json) => MessageRes.fromJson(json ?? {}),
    );
  }

  ///
  /// 設定本次結帳使用付款方式 (沒使用到)
  ///
  Future<MessageRes> postCartSetPayment(PaymentId paymentId) {
    return apiProvider.post(
      'cart/set/payment/${paymentId.value}',
      (json) => MessageRes.fromJson(json ?? {}),
    );
  }

  ///
  /// 設定本次結帳使用的折扣
  ///
  Future<MessageRes> postCartSetRebate(String rebateNumber) {
    return apiProvider.post(
      'cart/set/rebate/$rebateNumber',
      (json) => MessageRes.fromJson(json ?? {}),
    );
  }

  ///
  /// 購物車優惠卷列出
  ///
  Future<Iterable<VoucherRes>> getCartVouchers() {
    return apiProvider.getResData(
      'cart/vouchers',
      (json) => List.from(json ?? []).map((e) => VoucherRes.fromJson(e)),
    );
  }

  ///
  /// 新增一筆裝置ID
  ///
  Future<DevicesCreateRes> postDevicesIdCreate(
      DeviceType deviceType, String deviceId) {
    return apiProvider.post(
      'devices/${deviceType.value}/create',
      (json) => DevicesCreateRes.fromJson(json ?? {}),
      data: {
        'device_id': deviceId,
      },
    );
  }

  ///
  /// 取得常見問題
  ///
  Future<Iterable<Iterable<FaqRes>>> getFaq() {
    return apiProvider.getResData(
      'faq',
      (json) => List.from(json ?? []).map((e) {
        return List.from(e ?? []).map((e) => FaqRes.fromJson(e));
      }),
    );
  }

  ///
  /// 所有任選
  ///
  Future<Iterable<PromotionRes>> getPromotionsBuyNGetMDiscount() {
    return apiProvider.getResData(
      'promotions/buy_n_get_m_discount',
      (json) => List.from(json ?? []).map((e) => PromotionRes.fromJson(e)),
    );
  }

  ///
  /// 單一任選
  ///
  Future<PromotionDetailRes> getPromotionsBuyNGetMDiscountWithId(
      String id, PromotionIdReq query) {
    return apiProvider.getResData(
      'promotions/buy_n_get_m_discount/$id',
      (json) => PromotionDetailRes.fromJson(json ?? {}),
      filter: query.toJson(),
    );
  }

  ///
  /// 買N送M
  ///
  Future<PromotionDetailRes> getPromotionsBuyNGiftMWithId(
      String id, PromotionIdReq query) {
    return apiProvider.getResData(
      'promotions/buy_n_gift_m/$id',
      (json) => PromotionDetailRes.fromJson(json ?? {}),
      filter: query.toJson(),
    );
  }

  ///
  /// 加價購所有商品
  ///
  Future<PromotionsAddOnDiscountRes> getPromotionsAddOnDiscount() {
    return apiProvider.getResData(
      'promotions/add_on_discount',
      (json) => PromotionsAddOnDiscountRes.fromJson(json ?? {}),
    );
  }

  ///
  /// 所有加價購活動資訊
  ///
  Future<Iterable<PromotionsAddOnIdsRes>> getPromotionsAddOnIds() {
    return apiProvider.getResData(
      'promotions/add_on_ids',
      (json) =>
          List.from(json ?? []).map((e) => PromotionsAddOnIdsRes.fromJson(e)),
    );
  }

  ///
  /// 取得指定會員的發票證明聯
  ///
  Future<MembersOrdersInvoicesRes> getMembersOrdersInvoices(String id) {
    return apiProvider.getResData(
      'members/orders/$id/invoices',
      (json) => MembersOrdersInvoicesRes.fromJson(json),
    );
  }

  ///
  /// 取得所有訂單
  ///
  Future<Iterable<MembersOrdersRes>> getMembersOrders() async {
    final orders = await apiProvider.getResData(
      'members/orders',
      (json) => List.from(json ?? []).map((e) => MembersOrdersRes.fromJson(e)),
    );
    // save to local storage
    final box = boxProvider.getGsBox(Boxes.order.name);
    box.erase();
    for (final order in orders) {
      box.write(order.id ?? '', order.toJson());
    }
    return orders;
  }

  ///
  /// 取得所有訂單狀態數量
  ///
  Future<Map<String, dynamic>> getMembersOrdersCount() {
    return apiProvider.getResData(
      'members/orders/count',
      (json) => json ?? {},
    );
  }

  ///
  /// 取得一筆訂單
  ///
  Future<MembersOrdersRes> getMembersOrdersWithId(String id) async {
    final order = await apiProvider.getResData(
      'members/orders/$id',
      (json) => MembersOrdersRes.fromJson(json),
    );
    // save to local storage
    final box = boxProvider.getGsBox(Boxes.order.name);
    box.write(order.id ?? '', order.toJson());
    return order;
  }

  ///
  /// 訂單取消
  ///
  Future<MessageRes> postMembersOrdersCancel(String id) async {
    final res = await apiProvider.patch(
      'members/orders/$id/cancel',
      (json) => MessageRes.fromJson(json ?? {}),
    );
    try {
      // get order detail
      final data = await getMembersOrdersWithId(id);
      await FirebaseAnalytics.instance.logRefund(
        currency: 'TWD',
        // coupon: data.coupon,
        value: data.subtotal?.toDouble(),
        // tax: data.otherMoney?.toDouble(),
        shipping: data.shippingFee?.toDouble(),
        transactionId: data.number,
        // affiliation: data.affiliation,
        items: data.products
            ?.map((e) => e.toAnalyticsEventItem())
            .toList(growable: false),
      );
    } catch (e, s) {
      talker.handle(e, s, '[GA] logRefund failed');
    }

    return res;
  }

  ///
  /// 取得特定訂單的詢問記錄
  ///
  Future<Iterable<MembersOrdersQuestionsRes>> getMembersOrdersQuestions(
      String id) {
    return apiProvider.getResData(
      'members/orders/$id/questions',
      (json) => List.from(json ?? [])
          .map((e) => MembersOrdersQuestionsRes.fromJson(e)),
    );
  }

  ///
  /// 新增一筆特定訂單的詢問記錄
  ///
  Future<MessageRes> postMembersOrdersQuestions(
      String id, MembersOrdersQuestionsPostReq data) {
    return apiProvider.post(
      'members/orders/$id/questions',
      data: data.toJson(),
      (json) => MessageRes.fromJson(json ?? {}),
    );
  }

  ///
  /// 取得物流記錄
  ///
  Future<Iterable<MembersOrdersMessage>> getMembersOrdersShips(String id) {
    return apiProvider.getResData(
      'members/orders/$id/ships',
      (json) =>
          List.from(json ?? []).map((e) => MembersOrdersMessage.fromJson(e)),
    );
  }

  ///
  /// 取得退貨物流
  ///
  Future<Iterable<MembersOrdersMessage>> getMembersOrdersShipsRefunds(
      String id) {
    return apiProvider.getResData(
      'members/orders/$id/ships/refunds',
      (json) =>
          List.from(json ?? []).map((e) => MembersOrdersMessage.fromJson(e)),
    );
  }

  ///
  /// 取得退貨訊息
  ///
  Future<MembersOrdersRefundRes> getMembersOrdersRefundMessages(String id) {
    return apiProvider.getResData(
      'members/orders/$id/refund',
      (json) => MembersOrdersRefundRes.fromJson(json),
    );
  }

  ///
  /// 新增退貨 (使用者退貨資料 id 或 銀行代碼+名稱+戶名+帳號 擇一必填)
  ///
  Future<MessageRes> postMembersOrdersRefund(
      String id, MembersOrdersRefundPostReq data) {
    return apiProvider.post(
      'members/orders/$id/refund',
      data: data.toJson(),
      (json) => MessageRes.fromJson(json ?? {}),
    );
  }

  ///
  /// 取得訂單商品評價
  ///
  Future<Iterable<MembersOrdersComment>> getMembersOrdersComments(String id) {
    return apiProvider.getResData(
      'members/orders/$id/comments',
      (json) =>
          List.from(json ?? []).map((e) => MembersOrdersComment.fromJson(e)),
    );
  }

  ///
  /// 新增多筆訂單商品評價
  ///
  Future<MessageRes> postMembersOrdersComments(
      String id, Iterable<MembersOrdersCommentsPostReq> data) {
    return apiProvider.post(
      'members/orders/$id/comments',
      data: {
        'comments': jsonEncode(data.toList(growable: false)),
      },
      (json) => MessageRes.fromJson(json ?? {}),
    );
  }

  ///
  /// 意見回饋 - 新增
  ///
  Future<MessageRes> postFeedback(MembersFeedbacksReq data) {
    return apiProvider.post(
      'members/feedbacks',
      data: data.toJson(),
      (json) => MessageRes.fromJson(json ?? {}),
    );
  }

  ///
  /// 取得個人資料
  ///
  Future<MembersProfileRes> getMembersProfile() {
    return apiProvider.getResData(
      'members/profile',
      (json) => MembersProfileRes.fromJson(json ?? {}),
    );
  }

  ///
  /// 更新個人資料
  ///
  Future<MembersProfilePatchRes> patchMembersProfile(
      MembersProfilePatchReq data) async {
    final res = await apiProvider.patch(
      'members/profile',
      data: data.toJson(),
      (json) => MembersProfilePatchRes.fromJson(json ?? {}),
    );
    // 更新生日到 appier
    final birthday = res.profile?.birthdayAsDateTime;
    if (birthday != null) {
      await AppierFlutter.setDayOfBirth(birthday.day);
      await AppierFlutter.setMonthOfBirth(birthday.month);
      await AppierFlutter.setYearOfBirth(birthday.year);
    }
    return res;
  }

  ///
  /// 變更密碼
  ///
  Future<MessageRes> patchMembersPassword(MembersPasswordPatchReq data) {
    return apiProvider.patch(
      'members/password',
      data: data.toJson(),
      (json) => MessageRes.fromJson(json ?? {}),
    );
  }

  ///
  /// 取得個人的所有地址
  ///
  Future<Iterable<MembersAddressRes>> getMembersAddresses() {
    return apiProvider.getResData(
      'members/addresses',
      (json) => List.from(json ?? []).map((e) => MembersAddressRes.fromJson(e)),
    );
  }

  ///
  /// 刪除一筆個人地址
  ///
  Future<MessageRes> deleteMembersAddresses(String id) {
    return apiProvider.delete(
      'members/addresses/$id',
      (json) => MessageRes.fromJson(json ?? {}),
    );
  }

  ///
  /// 新增一組個人地址
  ///
  Future<MessageRes> postMembersAddresses(
      AddressType type, MembersAddressesPostReq data) {
    return apiProvider.post(
      'members/addresses/${type.value}',
      data: data.toJson(),
      (json) => MessageRes.fromJson(json ?? {}),
    );
  }

  ///
  /// 更新一筆個人的地址，不傳入的欄位表示不更新
  ///
  Future<MessageRes> patchMembersAddresses(
      AddressType type, String id, MembersAddressesPostReq data) {
    return apiProvider.patch(
      'members/addresses/${type.value}/$id',
      data: data.toJson(),
      (json) => MessageRes.fromJson(json ?? {}),
    );
  }

  ///
  /// 取得個人的所有退貨資料
  ///
  Future<Iterable<MembersRefundRes>> getMembersRefund() {
    return apiProvider.getResData(
      'members/refund',
      (json) => List.from(json ?? []).map((e) => MembersRefundRes.fromJson(e)),
    );
  }

  ///
  /// 新增一組個人退貨資料
  ///
  Future<MessageRes> postMembersRefund(MembersRefundPostReq data) {
    return apiProvider.post(
      'members/refund',
      data: data.toJson(),
      (json) => MessageRes.fromJson(json ?? {}),
    );
  }

  ///
  /// 更新一筆個人的退貨資料，不傳入的欄位表示不更新
  ///
  Future<MessageRes> patchMembersRefund(
      String refundId, MembersRefundPostReq data) {
    return apiProvider.patch(
      'members/refund/$refundId',
      data: data.toJson(),
      (json) => MessageRes.fromJson(json ?? {}),
    );
  }

  ///
  /// 刪除一筆個人退貨資料
  ///
  Future<MessageRes> deleteMembersRefund(String refundId) {
    return apiProvider.delete(
      'members/refund/$refundId',
      (json) => MessageRes.fromJson(json ?? {}),
    );
  }

  ///
  /// 新增 Preorder 貨到通知
  ///
  Future<ProductsPreorderPostRes> postMemberPreorders(
      String productId, String email) {
    return apiProvider.post(
      'products/$productId/preorder',
      (json) => ProductsPreorderPostRes.fromJson(json ?? {}),
      data: {'email': email},
    );
  }

  ///
  /// 取得 Preorder 貨到通知
  ///
  Future<Iterable<MembersPreordersRes>> getMemberPreorders() {
    return apiProvider.getResData(
      'members/preorders',
      (json) =>
          List.from(json ?? []).map((e) => MembersPreordersRes.fromJson(e)),
    );
  }

  ///
  /// 刪除該使用者的所有 Preorder 貨到通知
  ///
  Future<MessageRes> deleteMemberPreorders() {
    return apiProvider.delete(
      'members/preorders',
      (json) => MessageRes.fromJson(json ?? {}),
    );
  }

  ///
  /// 刪除一筆貨到通知
  ///
  Future<MessageRes> deleteMemberPreorder(String id) {
    return apiProvider.delete(
      'members/preorders/$id',
      (json) => MessageRes.fromJson(json ?? {}),
    );
  }

  ///
  /// 修改此使用者的推播偏好設定
  ///
  Future<MembersMessagesPreferenceRes> postMembersMessagesPreference(
      SwitchStatus action) {
    return apiProvider.post(
      'members/messages/preference/${action.value}',
      (json) => MembersMessagesPreferenceRes.fromJson(json ?? {}),
    );
  }

  ///
  /// 客服紀錄
  ///
  Future<Iterable<MembersMessagesQuestion>> getMembersMessagesQuestions() {
    return apiProvider.getResData(
      'members/messages/questions',
      (json) =>
          List.from(json ?? []).map((e) => MembersMessagesQuestion.fromJson(e)),
    );
  }

  ///
  /// 優惠活動 (推播)
  ///
  Future<Iterable<MembersMessagesActivity>> getMembersMessagesActivities() {
    return apiProvider.getResData(
      'members/messages/activities',
      (json) =>
          List.from(json ?? []).map((e) => MembersMessagesActivity.fromJson(e)),
    );
  }

  ///
  /// 未登入優惠活動 (推播)
  ///
  Future<Iterable<MembersMessagesActivity>>
      getMembersMessagesActivitiesNologin() {
    return apiProvider.getResData(
      'members/messages/activities/nologin',
      (json) =>
          List.from(json ?? []).map((e) => MembersMessagesActivity.fromJson(e)),
    );
  }

  ///
  /// 通知消息 (訂單狀態改變)
  ///
  Future<Iterable<MembersMessagesOrder>> getMembersMessagesOrders() {
    return apiProvider.getResData(
      'members/messages/orders',
      (json) =>
          List.from(json ?? []).map((e) => MembersMessagesOrder.fromJson(e)),
    );
  }

  ///
  /// 物流通知 (推播)
  ///
  Future<Iterable<MembersMessagesShip>> getMembersMessagesShips() {
    return apiProvider.getResData(
      'members/messages/ships',
      (json) =>
          List.from(json ?? []).map((e) => MembersMessagesShip.fromJson(e)),
    );
  }

  ///
  /// 取得訊息中心摘要
  ///
  Future<Iterable<MembersMessagesSummary>> getMembersMessagesSummarys() {
    return apiProvider.getResData(
      'members/messages/summarys',
      (json) =>
          List.from(json ?? []).map((e) => MembersMessagesSummary.fromJson(e)),
    );
  }

  ///
  /// 優惠活動清空 (DB 標記為前端不可讀取)
  ///
  Future<MessageRes> patchMembersMessagesActivitiesClear() {
    return apiProvider.patch(
      'members/messages/activities/clear',
      (json) => MessageRes.fromJson(json!),
    );
  }

  ///
  /// 推播已讀取
  ///
  Future<MessageRes> putMembersMessagesRead(MessageType type) {
    return apiProvider.put(
      'members/messages/read/${type.value}',
      (json) => MessageRes.fromJson(json!),
    );
  }

  ///
  /// 推播未讀取數量
  ///
  Future<MembersMessagesUnreadRes> getMembersMessagesUnread() async {
    final res = await apiProvider.getResData(
      'members/messages/unread',
      (json) => MembersMessagesUnreadRes.fromJson(json!),
    );
    prefProvider.unreadRes = res;
    try {
      _setBadgeCount(res.allUnreadCount.toInt());
    } catch (e, s) {
      talker.handle(e, s, '[Badge] setBadgeCount failed');
    }
    return res;
  }

  ///
  /// set badge count
  ///
  Future<void> _setBadgeCount(int count) async {
    try {
      if (await FlutterAppBadger.isAppBadgeSupported()) {
        await FlutterAppBadger.updateBadgeCount(count);
      }
    } catch (e, s) {
      talker.handle(e, s, '[Badge] setBadgeCount failed');
    }
  }

  ///
  /// 優惠卷
  ///
  Future<Iterable<VoucherRes>> getMembersMessagesVouchers(
      [VoucherStage stage = VoucherStage.app]) {
    return apiProvider.getResData(
      'members/messages/vouchers/${stage.name}',
      (json) => List.from(json ?? []).map((e) => VoucherRes.fromJson(e)),
    );
  }

  ///
  /// 取得搜尋紀錄
  ///
  Future<Iterable<String>> getMembersSearchHistories() {
    return apiProvider.getResData(
      'members/search_histories',
      (json) => List<String>.from(json ?? []).map((e) => e.toString()),
    );
  }

  ///
  /// 新增搜尋紀錄
  ///
  Future<MessageRes> postMembersSearchHistories(String keyword) {
    return apiProvider.post(
      'members/search_histories',
      (json) => MessageRes.fromJson(json!),
      data: {'keywords': keyword},
    );
  }

  ///
  /// 刪除該使用者的所有搜尋紀錄
  ///
  Future<MessageRes> deleteMembersSearchHistories() {
    return apiProvider.delete(
      'members/search_histories',
      (json) => MessageRes.fromJson(json!),
    );
  }

  ///
  /// 取得我的收藏
  ///
  Future<MembersMyFavoriteRes> getMembersMyFavorite() {
    return apiProvider.getResData(
      'members/my_favorite',
      (json) => MembersMyFavoriteRes.fromJson(json),
    );
  }

  ///
  /// 同步遠端我的收藏到本地
  ///
  Future<void> fetchMembersMyFavorite() async {
    try {
      final res = await getMembersMyFavorite();
      final box = boxProvider.getGsBox(Boxes.favorite.name);
      await box.erase();
      for (final item in (res.items ?? {}).entries) {
        final it = item.value.map((e) => e.toJson());
        await box.write(item.key, it.toList(growable: false));
      }
    } catch (e, s) {
      talker.handle(e, s, '[Favorite] fetchMembersMyFavorite failed');
    }
  }

  ///
  /// 刪除所有收藏
  ///
  Future<MessageRes> deleteMembersMyFavorite() {
    return apiProvider.delete(
      'members/my_favorite',
      (json) => MessageRes.fromJson(json!),
    );
  }

  ///
  /// 同步收藏
  ///
  Future<MembersMyFavoritePutRes> putMembersMyFavorite(
      Map<String, String> data) {
    return apiProvider.put(
      'members/my_favorite',
      (json) => MembersMyFavoritePutRes.fromJson(json!),
      data: {'client_items': jsonEncode(data)},
    );
  }

  ///
  /// 新增收藏
  ///
  Future<MessageRes> postMembersMyFavorite(MembersMyFavoritePostReq data) {
    return apiProvider.post(
      'members/my_favorite',
      (json) => MessageRes.fromJson(json!),
      data: data.toJson(),
    );
  }

  ///
  /// 取得收藏數量
  ///
  Future<int> getMembersMyFavoriteCount() {
    return apiProvider.getResData(
      'members/my_favorite/count',
      (json) => json['count'] ?? 0,
    );
  }

  ///
  /// 取得是否為收藏商品
  ///
  Future<bool> isMembersMyFavorite(String id) {
    return apiProvider.getResData(
      'members/my_favorite/$id',
      (json) => json['is_favorite'] ?? false,
    );
  }

  ///
  /// 移除收藏
  ///
  Future<MessageRes> deleteMembersMyFavoriteWithId(String id) {
    return apiProvider.delete(
      'members/my_favorite/$id',
      (json) => MessageRes.fromJson(json!),
    );
  }

  ///
  /// 取得會員紅利點數資料
  ///
  Future<MembersMyBonusRes> getMembersMyBonus() {
    return apiProvider.getResData(
      'members/my_bonus',
      (json) => MembersMyBonusRes.fromJson(json),
    );
  }

  ///
  /// 取得會員彈窗資料
  /// stage: 平台種類, Available values: app, mobile
  ///
  Future<MembersPopupRes> getMembersPopup([String stage = 'app']) {
    return apiProvider.getResData(
      'members/popup/$stage',
      (json) {
        if (json is List) {
          final it = List.from(json).map((e) => MembersPopupRes.fromJson(e));
          if (it.isNotEmpty) {
            return it.elementAt(0);
          }
        }
        return MembersPopupRes.fromJson(json);
      },
    );
  }

  ///
  /// 彈窗完成
  ///
  Future<MessageRes> putMembersPopup(num id) {
    return apiProvider.put(
      'members/popup/$id',
      (json) => MessageRes.fromJson(json ?? {}),
    );
  }

  ///
  /// 取得所有設定值
  ///
  Future<ConfigsRes> getConfigs() {
    return apiProvider.getResData(
      'configs',
      (json) => ConfigsRes.fromJson(json),
    );
  }

  ///
  /// 取結帳前預覽資訊
  ///
  Future<CheckoutRes> getCheckout() {
    return apiProvider.getResData(
      'checkout',
      (json) => CheckoutRes.fromJson(json),
    );
  }

  ///
  /// post 結帳表單
  ///
  Future<CheckoutPostRes> postCheckout(CheckoutPostReq data) {
    return apiProvider.post(
      'checkout',
      (json) => CheckoutPostRes.fromJson(json!),
      data: data.toJson(),
    );
  }

  /// 付款方式選擇頁
  ///
  Future<PaymentOptionsRes> getPaymentOptions() {
    return apiProvider.getResData(
      'payment/options',
      (json) => PaymentOptionsRes.fromJson(json),
    );
  }

  ///
  /// line 從 code 取得 token
  ///
  Future<LineTokenRes> getLineToken(LineTokenReq req) async {
    final data = req.toJson();
    data.removeNull();
    final res = await apiProvider.dio.postUri<Map<String, dynamic>>(
      Uri.https('api.line.me', 'oauth2/v2.1/token'),
      data: FormData.fromMap(data),
    );
    return LineTokenRes.fromJson(res.data ?? {});
  }

  ///
  /// 漢堡選單
  ///
  Future<Iterable<MenuRes>> getMenus() async {
    final res = await apiProvider.getResData(
      'menus',
      (json) => List.from(json ?? []).map((e) => MenuRes.fromJson(e)),
    );
    final ls = res.toList(growable: false);
    for (var element in ls) {
      element.updateChildren();
    }
    return ls;
  }

  ///
  /// 頁尾
  ///
  Future<String> getFooter() {
    return apiProvider.getResData(
      'footer',
      (json) => json['html'] ?? '',
    );
  }
}
