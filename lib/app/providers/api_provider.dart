import 'dart:io';

import 'package:dio/dio.dart';
import 'package:efshop/app/models/error_res.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/error_type.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:get/utils.dart';

import 'package:talker_flutter/talker_flutter.dart';

class ApiProvider {
  final Dio dio;
  final PrefProvider prefProvider;
  final String authority;

  Talker get talker => prefProvider.talker;

  ApiProvider({
    required this.dio,
    required this.prefProvider,
  }) : authority = prefProvider.host;

  Dio get httpClient {
    dio.options.contentType = Headers.formUrlEncodedContentType;
    if (prefProvider.token.isNotEmpty) {
      dio.options.headers['Authorization'] = 'Bearer ${prefProvider.token}';
    }
    dio.options.headers['accept'] = 'application/json';
    final version = prefProvider.packageInfo.version;
    // var email = prefProvider.profile.email ?? '';
    // email = email.isEmail ? email : '<EMAIL>';
    const email = '<EMAIL>';
    dio.options.headers['User-Agent'] =
        'Efshop/$version ($email; +https://efshop.com.tw/)';
    return dio;
  }

  ///
  /// patch 通用模版
  ///
  Future<T> patch<T>(
    String unencodedPath,
    T Function(Map<String, dynamic>? json) creator, {
    Map<String, dynamic>? data,
  }) async {
    data ??= <String, dynamic>{};
    data.removeNull();
    final res = await httpClient.patchUri<Map<String, dynamic>>(
      Uri.https(authority, 'v1/$unencodedPath'),
      // data: FormData.fromMap(data),
      data: data,
    );
    // 處理錯誤
    if (res.data?[Keys.status] == false) {
      final errorRes = ErrorRes.fromJson(res.data!);
      throw errorRes;
    }
    final ret = creator.call(res.data);
    if (ret != null) {
      return ret;
    }
    throw unencodedPath;
  }

  ///
  /// delete 通用模版
  ///
  Future<T> delete<T>(
    String unencodedPath,
    T Function(Map<String, dynamic>? json) creator, {
    Map<String, dynamic>? data,
  }) async {
    data ??= <String, dynamic>{};
    data.removeNull();
    final res = await httpClient.deleteUri<Map<String, dynamic>>(
      Uri.https(authority, 'v1/$unencodedPath'),
      data: FormData.fromMap(data),
    );
    // 處理錯誤
    if (res.data?[Keys.status] == false) {
      final errorRes = ErrorRes.fromJson(res.data!);
      throw errorRes;
    }
    final ret = creator.call(res.data);
    if (ret != null) {
      return ret;
    }
    throw unencodedPath;
  }

  ///
  /// post 通用模版
  ///
  Future<T> post<T>(
    String unencodedPath,
    T Function(Map<String, dynamic>? json) creator, {
    Map<String, dynamic>? data,
  }) async {
    data ??= <String, dynamic>{};
    data.removeNull();
    try {
      final res = await httpClient.postUri<Map<String, dynamic>>(
        Uri.https(authority, 'v1/$unencodedPath'),
        data: FormData.fromMap(data),
      );
      // 處理錯誤
      if (res.data?[Keys.status] == false) {
        final errorRes = ErrorRes.fromJson(res.data!);
        throw errorRes;
      }
      final ret = creator.call(res.data);
      if (ret != null) {
        return ret;
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw ErrorRes.fromJson(e.response?.data ?? {});
      }
      throw ErrorRes(status: false, error: e.error.toString());
    }
    throw unencodedPath;
  }

  ///
  /// put 通用模版
  ///
  Future<T> put<T>(
    String unencodedPath,
    T Function(Map<String, dynamic>? json) creator, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? filter,
  }) async {
    data ??= <String, dynamic>{};
    data.removeNull();
    final res = await httpClient.putUri<Map<String, dynamic>>(
      Uri.https(authority, 'v1/$unencodedPath', filter?.toStringMap()),
      data: FormData.fromMap(data),
    );
    // 處理錯誤
    if (res.data?[Keys.status] == false) {
      final errorRes = ErrorRes.fromJson(res.data!);
      throw errorRes;
    }
    final ret = creator.call(res.data);
    if (ret != null) {
      return ret;
    }
    throw unencodedPath;
  }

  ///
  /// 取得原始物件通用模板
  ///
  /// message: "This exception was thrown because the response has a status code of 401 and RequestOptions.validateStatus was configured to thro…"
  /// response: Response ({"status":false,"type":"AuthorizationError","error":"Token not found"})
  ///   data: Map (3 items)
  ///     0: "status" -> false
  ///     1: "type" -> "AuthorizationError"
  ///     2: "error" -> "Token not found"
  ///   statusCode: 401
  ///   statusMessage: "Unauthorized"
  Future<T> getResData<T>(
    String unencodedPath,
    T Function(dynamic json) creator, {
    Map<String, dynamic>? filter,
  }) async {
    filter ??= <String, dynamic>{};
    filter.removeNull();
    // add cache_version
    final cacheVersion = prefProvider.configs.cacheVersion;
    if (cacheVersion != null && cacheVersion.isNotEmpty) {
      filter['cache_version'] = cacheVersion;
    }
    final uri = Uri.https(authority, 'v1/$unencodedPath', filter.toStringMap());

    try {
      final res = await httpClient.getUri(uri);
      // 處理錯誤
      if (res.data is Map && res.data?[Keys.status] == false) {
        final errorRes = ErrorRes.fromJson(res.data!);
        throw errorRes;
      }
      if (res.data != null) {
        return creator.call(res.data);
      }
    } on DioException catch (e) {
      if (e.response?.data is Map && e.response?.data?[Keys.status] == false) {
        final errorRes = ErrorRes.fromJson(e.response!.data!);
        if (errorRes.type == ErrorType.unauthorized) {
          throw ErrorType.unauthorized;
        }
        throw errorRes;
      }
      if (e.error is SocketException) {
        throw ErrorType.disconnected;
      }
      rethrow;
    }

    throw uri.path;
  }

  ///
  /// 取得單一物件通用模板
  ///
  // Future<T> getData<T>(
  //   String unencodedPath,
  //   T Function(dynamic json) creator, {
  //   Map<String, dynamic>? filter,
  // }) async {
  //   filter ??= <String, dynamic>{};
  //   filter.removeNull();
  //   final uri = Uri.https(authority, unencodedPath, filter.toStringMap());
  //   final res = await httpClient.getUri<Map>(uri);
  //   if (res.data.containsKey(Keys.Data)) {
  //     final json = res.data[Keys.Data];
  //     return creator?.call(json);
  //   }
  //   if (res.data.containsKey(Keys.Error.value)) {
  //     final e = ResError.fromJson(res.data[Keys.Error.value]);
  //     throw e.localMessage;
  //   }
  //   throw uri.path;
  // }
}
