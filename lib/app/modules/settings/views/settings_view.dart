import 'package:efshop/app/components/ef_share.dart';
import 'package:efshop/app/models/product_detail.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/settings_controller.dart';

class SettingsView extends GetView<SettingsController> {
  const SettingsView({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('設定'),
        centerTitle: true,
      ),
      body: controller.obx(
        (state) => _body(),
        // onEmpty: const Center(child: Text('Empty')),
        // onError: (error) => Center(child: Text(error ?? '')),
      ),
    );
  }

  Widget _body() {
    return ListView(
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield Obx(() {
      return SwitchListTile.adaptive(
        activeColor: EfColors.primary,
        value: controller.isAllowNotification.isOn,
        onChanged: (value) async {
          try {
            final res = await controller.updateNotification(value);
            if (res.status != true) {
              Get.showAlert(res.message ?? '');
            }
          } catch (e) {
            Get.showAlert(e.toString());
          }
        },
        title: const Text('推播通知'),
      );
    });
    yield const Divider(height: 1);
    yield ListTile(
      title: const Text('分享APP'),
      onTap: () {
        // 分享APP
        // Share.share('https://efshop.tw/app');
        EfShare(
          ProductDetail(
            shareUrl: Constants.shareApp.toString(),
          ),
          flutterShareMe: Get.find(),
          talker: controller.talker,
        ).sheet();
      },
      trailing: const Icon(Icons.chevron_right),
    );
    yield const Divider(height: 1);
    yield ListTile(
      title: const Text('會員條款'),
      onTap: () {
        // 前往會員條款
        Get.toNamed(Routes.EF_WEB, parameters: {
          Keys.url: Constants.uriPrivacy.toString(),
          Keys.title: '會員條款',
        });
      },
      trailing: const Icon(Icons.chevron_right),
    );
    yield const Divider(height: 1);
    yield ListTile(
      leading: const Text(
        'APP 版本',
        style: TextStyle(
          color: Colors.grey,
        ),
      ),
      title: Text(controller.displayVersion),
      // onTap: () {},
    );
    yield const SizedBox(height: 15);
    yield ListTile(
      title: const Text('刪除會員帳號'),
      onTap: () {
        Get.toNamed(Routes.DELETE_ACCOUNT);
      },
      trailing: const Icon(Icons.chevron_right),
    );
    yield const SizedBox(height: 15);
    yield ListTile(
      title: const Center(child: Text('登出')),
      onTap: _signOut,
    );
  }

  Future<void> _signOut() async {
    final res = await Get.showConfirm('即將登出', textConfirm: '登出');
    if (res == Button.confirm) {
      try {
        Get.showLoading();
        await controller.signOut();
        await controller.removeCookie();
        // await controller.facebookLogout();
        Get.back(); // 隱藏 loading
        // Get.back(result: IndexTab.home); // 回到個人中心
        await Get.restartIndex();
      } catch (e) {
        Get.back();
        Get.showAlert(e.toString());
      }
    }
  }
}
