import 'dart:async';

import 'package:efshop/app/models/members_orders_res.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/basic.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';
import 'package:stream_transform/stream_transform.dart';

class OrdersController extends GetxController
    with StateMixin<String>, GetSingleTickerProviderStateMixin {
  final WabowProvider wabowProvider;
  Talker get talker => wabowProvider.talker;
  PrefProvider get prefProvider => wabowProvider.prefProvider;
  BoxProvider get boxProvider => wabowProvider.boxProvider;
  final data = <MembersOrdersRes>[].obs;

  Map<OrderStatus, num> get tabs => prefProvider.tabs;
  final _disposable = Completer();
  late TabController _tabController;
  TabController get tabController => _tabController;
  // page controller
  final _pageController = PageController().obs;
  PageController get pageController => _pageController.value;
  PageController get newPageController {
    _pageController.value.dispose();
    final currentIndex = tabs.isEmpty ? 0 : tabController.index;
    _pageController.value = PageController(
      initialPage: currentIndex,
      keepPage: false,
      // viewportFraction: 0.99999,
      viewportFraction: 1,
    );
    return _pageController.value;
  }

  OrdersController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
    final tabCount = tabs.isEmpty ? 1 : tabs.length;
    _tabController = TabController(
      vsync: this,
      length: tabCount,
    );
    final box = boxProvider.getGsBox(Boxes.order.name);
    box
        .watch()
        .debounce(200.milliseconds)
        .takeUntil(_disposable.future)
        .listen((event) {
      talker.info('[OrdersController] order box changed');
      _fetchOrderCountFromLocalStorage();
      prefProvider.tabs.refresh();
    });
    if (Get.parameters.containsKey(Keys.id) && tabs.isNotEmpty) {
      final targetIndex = int.parse(Get.parameters[Keys.id] ?? '0');
      final safeIndex = targetIndex.clamp(0, tabs.length - 1);
      tabController.index = safeIndex;
    }
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    pageController.dispose();
    tabController.dispose();
    _disposable.complete();
    super.onClose();
  }

  // TODO: use iterable map
  Iterable<MembersOrdersRes> getOrdersWithFilter(
      Predicate<MembersOrdersRes> tester) sync* {
    final box = boxProvider.getGsBox(Boxes.order.name);
    final values = box.getValues();
    for (final json in values) {
      // filter
      final order = MembersOrdersRes.fromJson(json);
      if (tester(order)) {
        yield order;
      }
    }
  }

  // Iterable<MembersOrdersRes> getOrders() {
  //   return getOrdersWithFilter((element) {
  //     if (currentTab == OrderStatus.all) {
  //       return true;
  //     } else {
  //       return currentTab == element.orderStatus;
  //     }
  //   });
  // }

  Future<void> _fetchOrderCountFromLocalStorage() async {
    // tabs loop
    tabs.forEach((key, value) {
      final orders = getOrdersWithFilter((e) {
        if (key == OrderStatus.all) {
          return true;
        } else {
          return key == e.orderStatus;
        }
      });
      tabs[key] = orders.length;
    });
  }

  Future<void> _fetchOrderCount() async {
    final res = await wabowProvider.getMembersOrdersCount();
    for (var entity in res.entries) {
      final status = tabs.keys.firstWhere(
        (key) => key.display == entity.key,
        orElse: () => OrderStatus.all,
      );
      tabs[status] = entity.value;
    }
  }

  Future<void> onRefresh() async {
    try {
      change('', status: RxStatus.success());
      // _fetchOrderCount();
      await wabowProvider.getMembersOrders();
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }
}
