import 'dart:async';

import 'package:efshop/app/models/members_my_favorite_res.dart';
import 'package:efshop/app/models/message_res.dart';
import 'package:efshop/app/models/product_detail.dart';
import 'package:efshop/app/models/products_preorder_post_res.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:talker_flutter/talker_flutter.dart';

class FavoriteController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  PrefProvider get prefProvider => wabowProvider.prefProvider;
  BoxProvider get boxProvider => prefProvider.boxProvider;
  Talker get talker => wabowProvider.talker;
  final data = <MembersMyFavorite>[].obs;
  final _disposable = Completer();

  FavoriteController({required this.wabowProvider});

  @override
  void onInit() {
    super.onInit();
    final box = boxProvider.getGsBox(Boxes.favorite.name);
    box
        .watch()
        .debounce(200.milliseconds)
        .takeUntil(_disposable.future)
        .listen((event) {
      talker.info('Favorite box changed');
      // _loadFromStorage();
    });
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      _loadFromStorage();
      if (prefProvider.isLogin) {
        try {
          await wabowProvider.fetchMembersMyFavorite();
          _loadFromStorage();
        } catch (e) {
          talker.error(e);
        }
      }
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  Future<void> _loadFromStorage() async {
    try {
      final it = await _getFavorites();
      data.assignAll(it);
      change('', status: data.isEmpty ? RxStatus.empty() : RxStatus.success());
    } catch (e) {
      talker.error(e);
      // 強制顯示空白頁面
      change('', status: RxStatus.empty());
    }
  }

  Future<Iterable<MembersMyFavorite>> _getFavorites() async {
    final res = <MembersMyFavorite>[];
    final box = boxProvider.getGsBox(Boxes.favorite.name);
    final keys = [...box.getKeys<Iterable<String>>()];
    for (final key in keys) {
      final it = await getChildrenWithParentId(key);
      if (it.isNotEmpty) {
        res.add(it.first);
      }
    }
    return res;
  }

  Future<Iterable<MembersMyFavorite>> getChildrenWithParentId(String id) async {
    final box = boxProvider.getGsBox(Boxes.favorite.name);
    final val = box.read(id);
    // remote data
    if (val is List) {
      return val.map((e) => MembersMyFavorite.fromJson(e));
    }
    // local data
    if (val is String) {
      final product = _getProductWithNumberFromLocalStorage(id);
      if (product != null) {
        final it = await _getProductsGroup(product);
        return it;
      }
    }
    try {
      final product = await wabowProvider.getProducts(id);
      final it = await _getProductsGroup(product);
      return it;
    } catch (e) {
      talker.error(e);
    }
    return [];
  }

  ProductDetail? _getProductWithNumberFromLocalStorage(String number) {
    final box = boxProvider.getGsBox(Boxes.productDetail.name);
    final keys = [...box.getKeys<Iterable<String>>()];
    for (final key in keys) {
      final json = box.read(key);
      final product = ProductDetail.fromJson(json);
      if (product.number == number) {
        return product;
      }
    }
    return null;
  }

  Future<Iterable<MembersMyFavorite>> _getProductsGroup(
      ProductDetail product) async {
    final productJson = product.toJson();
    final it = await wabowProvider.getProductsGroup(product.id ?? '');
    return it.where((element) => element.color == product.color).map((e) {
      final json = e.toJson();
      return MembersMyFavorite.fromJson({...productJson, ...json});
    });
  }

  Future<MessageRes> removeFromFavorite(MembersMyFavorite favorite) async {
    try {
      final number = favorite.number ?? '';
      if (number.isEmpty) {
        throw 'number is empty';
      }
      final box = boxProvider.getGsBox(Boxes.favorite.name);
      if (box.hasData(number)) {
        await box.remove(number);
      } else {
        final product = _getProductWithNumberFromLocalStorage(number);
        if (product != null) {
          // 取得所有相關商品
          final it = await _getProductsGroup(product);
          for (var element in it) {
            if (box.hasData(element.number ?? '')) {
              await box.remove(element.number ?? '');
              break;
            }
          }
        }
      }
      if (prefProvider.isLogin) {
        // 已登入的 key 是 parentId, 未登入的 key 是 number
        final id = favorite.parentId ?? '';
        if (box.hasData(id)) {
          await box.remove(id);
        }
        return wabowProvider.deleteMembersMyFavoriteWithId(number);
      }
      return MessageRes();
    } finally {
      _loadFromStorage();
    }
  }

  Future<MessageRes> clearFavorite() async {
    try {
      final box = boxProvider.getGsBox(Boxes.favorite.name);
      await box.erase();
      if (prefProvider.isLogin) {
        return wabowProvider.deleteMembersMyFavorite();
      }
      return MessageRes();
    } finally {
      _loadFromStorage();
    }
  }

  Future<MessageRes> addToCart(MembersMyFavorite element) {
    final id = element.id ?? '';
    if (id.isNotEmpty) {
      return wabowProvider.postCart(id, 1);
    }
    throw 'no data';
  }

  Future<ProductsPreorderPostRes> addToPreorder(MembersMyFavorite element) {
    final res = prefProvider.loginRes;
    final email = res?.payload?.email ?? '';
    final id = element.id ?? '';
    if (id.isNotEmpty) {
      return wabowProvider.postMemberPreorders(id, email);
    }
    throw 'no data';
  }
}
