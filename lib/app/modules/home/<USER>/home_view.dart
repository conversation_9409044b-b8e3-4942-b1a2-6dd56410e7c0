import 'dart:math' show max;

import 'package:carousel_slider/carousel_slider.dart';
import 'package:dots_indicator/dots_indicator.dart';
import 'package:efshop/app/components/action_button.dart';
import 'package:efshop/app/components/background.dart';
import 'package:efshop/app/components/error_button.dart';
import 'package:efshop/app/components/snow_widget.dart';
import 'package:efshop/app/components/thumbnail_image.dart';
import 'package:efshop/app/models/category.dart';
import 'package:efshop/app/models/url.dart';
import 'package:efshop/app/modules/ef_grid/views/ef_grid_view.dart';
import 'package:efshop/app/modules/ef_menu/views/ef_menu_view.dart';
import 'package:efshop/app/modules/sale/views/sale_view.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/foundation.dart' show AsyncValueSetter;
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';

import '../controllers/home_controller.dart';
import 'dynamic_grid.dart';
import 'ef_search_delegate.dart';
import 'slide_grid.dart';
import 'slide_grid_product.dart';
import 'text_url_widget.dart';
import 'two_dimensions.dart';

class HomeView extends GetView<HomeController> {
  final AsyncValueSetter<IndexTab>? changeTab;
  final _pageViewKey = GlobalKey();

  HomeView({
    super.key,
    this.changeTab,
  }) {
    Get.lazyPut(
      () => HomeController(
        wabowProvider: Get.find(),
      ),
      fenix: true,
    );
    initializeController().then((value) {
      final controller = Get.find<HomeController>();
      controller.revertPage();
    });
  }

  Widget _leading() {
    return Transform.translate(
      offset: Offset(10.dw, 0),
      child: GestureDetector(
        onTap: () {
          // TEST:
          // final uri =Uri.parse(Constants.testUrl);
          // Get.launchUrl(uri);
          controller.prefProvider.showDeveloperMenu();
        },
        child: SvgPicture.asset(
          'assets/images/header_logo.svg',
          width: 76.dw,
          height: 38.dh,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeController>(
      init: Get.find<HomeController>(),
      builder: (controller) {
        return controller.obx(
          (state) {
            return Scaffold(
              backgroundColor: Colors.white,
              appBar: AppBar(
                leadingWidth: 96.dw,
                leading: _leading(),
                // title: const Text('HomeView'),
                centerTitle: true,
                actions: _actions(context).toList(growable: false),
                bottom: _preferredSizeWidget(),
              ),
              body: LayoutBuilder(builder: (context, constraints) {
                final width = max(constraints.maxWidth, 1.0);
                final height = max(constraints.maxHeight, 1.0);
                controller.viewPort = Size(width, height);
                return _body();
              }),
            );
          },
          onError: (error) {
            return ErrorButton(
              error,
              onTap: controller.onRefresh,
            );
          },
        );
      },
    );
  }

  Widget _body() {
    final it = controller.tabs;
    if (it.isEmpty) {
      return const SizedBox();
    }
    return PageView.builder(
      controller: controller.pageController,
      itemCount: it.length,
      onPageChanged: (value) {
        controller.talker.debug('[HomeView] onPageChanged: $value');
        controller.tabController?.animateTo(value);
      },
      itemBuilder: (BuildContext context, int index) {
        final category = it.elementAt(index);
        return _pageView(category);
      },
    );
  }

  // Widget _body2() {
  //   Iterable<Widget> children() sync* {
  //     for (final entry in controller.tabs) {
  //       yield _pageView(entry);
  //     }
  //   }
  //   return TabBarView(
  //     children: children().toList(growable: false),
  //   );
  // }

  Widget _homeBody() {
    return NotificationListener(
      onNotification: (notification) {
        // controller.talker.info(
        //     '[HomeView] notification.runtimeType: ${notification.runtimeType}');
        if (notification is ScrollUpdateNotification) {
          // controller.talker.info(
          //     '[HomeView] notification.runtimeType: ${notification.runtimeType}');
          // controller.scrollOffset = notification.metrics.pixels;
        } else if (notification is ScrollEndNotification) {
          // controller.talker.info(
          //     '[HomeView] notification.runtimeType: ${notification.runtimeType}');
          // controller.scrollOffset = notification.metrics.pixels;
        } else if (notification is ScrollStartNotification) {
          controller.talker.info(
              '[HomeView] notification.runtimeType: ${notification.runtimeType}');
          // controller.scrollOffset = notification.metrics.pixels;
        } else if (notification is UserScrollNotification) {
          controller.talker.info(
              '[HomeView] notification.runtimeType: ${notification.runtimeType}');
          // controller.scrollOffset = notification.metrics.pixels;
        } else if (notification is OverscrollNotification) {
          controller.talker.info(
              '[HomeView] notification.runtimeType: ${notification.runtimeType}');
          // controller.scrollOffset = notification.metrics.pixels;
        }
        return false;
      },
      child: CustomScrollView(
        physics: const ClampingScrollPhysics(),
        controller: controller.scroll,
        slivers: _children().toList(growable: false),
      ),
    );
  }

  Widget _pageView(Category category) {
    // special case: home
    if (Keys.home == category.displayName) {
      return controller.moduleStatus.obx(
        (state) {
          return Background(
            background: _homeBody(),
            child: Visibility(
              visible: controller.snowing,
              child: IgnorePointer(
                ignoring: true,
                child: AspectRatio(
                  aspectRatio: controller.viewPort.aspectRatio,
                  child: SnowWidget(
                    isRunning: true,
                    totalSnow: 50,
                    speed: 0.05,
                    maxRadius: 8,
                    startSnowing: true,
                    snowColor: Colors.white,
                    imageUrl: controller.prefProvider.configs.snow?.image ?? '',
                  ),
                ),
              ),
            ),
          );
        },
        onError: (error) {
          return ErrorButton(
            error,
            onTap: controller.onRefresh,
          );
        },
      );
    }
    // special case: sale
    if (Keys.sale == category.displayName) {
      final pageController = controller.pageController;
      return SaleView(
          // onOverscroll: (value) {
          //   if (value > 0) {
          //     pageController.position
          //         .correctPixels(pageController.position.pixels + value);
          //     pageController.position.notifyListeners();
          //     pageController.nextPage(
          //       duration: const Duration(milliseconds: 300),
          //       curve: Curves.easeInOut,
          //     );
          //   } else if (value < 0) {
          //     pageController.position
          //         .correctPixels(pageController.position.pixels + value);
          //     pageController.position.notifyListeners();
          //     pageController.previousPage(
          //       duration: const Duration(milliseconds: 300),
          //       curve: Curves.easeInOut,
          //     );
          //   }
          // },
          );
    }
    final url = category.url ?? Url();
    final path = '/${url.action}';
    if (Routes.CATEGORY == path || Routes.PROMOTION == path) {
      return EfGridView(
        url: url,
        category: category,
      );
    }
    return const SizedBox();
  }

  Iterable<Widget> _children() sync* {
    for (final data in controller.indexModuleAppRes.values) {
      // talker.info(item.type);
      switch (data.typeEnum) {
        case IndexModuleType.textUrl:
          yield SizedBox(height: Constants.gridSpacing.dh).sliverBox;
          // yield TextUrlWidget(data).sliverBox;
          yield TextUrlWidget.divider().sliverBox;
          yield TextUrlWidget(data);
          yield TextUrlWidget.divider().sliverBox;
          break;
        case IndexModuleType.slideGrids:
          yield SizedBox(height: Constants.gridSpacing.dh).sliverBox;
          yield SlideGrid(data, controller.viewPort.height).sliverBox;
          break;
        // 使用 EfGridView 的 SearchItem
        case IndexModuleType.slideGridsProducts:
          yield SizedBox(height: Constants.gridSpacing.dh).sliverBox;
          yield SlideGridProduct(
            data,
            wabowProvider: controller.wabowProvider,
          ).sliverBox;
          break;
        case IndexModuleType.dynamicGridOne:
          yield SizedBox(height: Constants.gridSpacing.dh).sliverBox;
          yield DynamicGrid(data, 1).sliverBox;
          break;
        case IndexModuleType.dynamicGridTwo:
          yield SizedBox(height: Constants.gridSpacing.dh).sliverBox;
          yield DynamicGrid(data, 2).sliverBox;
          break;
        case IndexModuleType.dynamicGridThree:
          yield SizedBox(height: Constants.gridSpacing.dh).sliverBox;
          yield DynamicGrid(data, 3).sliverBox;
          break;
        case IndexModuleType.dynamicGridFour:
          yield SizedBox(height: Constants.gridSpacing.dh).sliverBox;
          yield DynamicGrid(data, 4).sliverBox;
          break;
        case IndexModuleType.banners:
          yield SizedBox(height: Constants.gridSpacing.dh).sliverBox;
          yield _banner().sliverBox;
          break;
        case IndexModuleType.twoDimensions:
          yield SliverFillViewport(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                return Background(
                  background: TwoDimensions(
                    key: _pageViewKey,
                    data,
                    aspectRatio: controller.viewPort.aspectRatio,
                    talker: controller.talker,
                    pageIndex: controller.twoDimensionIndex,
                    onPageChanged: (value) =>
                        controller.twoDimensionIndex = value,
                    onOverscroll: (value) {
                      try {
                        controller.scroll.position.correctBy(value);
                        controller.scroll.position.notifyListeners();
                      } catch (e) {
                        controller.talker.error(e);
                      }
                    },
                    // scrollable: (scrollableArgs) {
                    //   var innerScrollable = false;
                    //   // 只有在最頂才需判斷是否可以滾動
                    //   if (controller.scroll.isAtTop) {
                    //     if (scrollableArgs.scrollDelta > 0) {
                    //       // down
                    //       innerScrollable = !scrollableArgs.metrics.isAtBottom;
                    //     } else if (scrollableArgs.scrollDelta < 0) {
                    //       // up
                    //       innerScrollable = !scrollableArgs.metrics.isAtTop;
                    //     }
                    //   }
                    //   // 無法滾動要執行以下
                    //   if (!innerScrollable) {
                    //     controller.scroll.position
                    //         .correctBy(scrollableArgs.scrollDelta);
                    //     controller.scroll.position.notifyListeners();
                    //   }
                    //   return innerScrollable;
                    // },
                  ),
                  child: Obx(() {
                    return Visibility(
                      visible: controller.cover,
                      child: const ColoredBox(
                        color: Colors.transparent,
                        child: SizedBox.expand(),
                      ),
                    );
                  }),
                );
              },
              childCount: 1,
            ),
          );
        case IndexModuleType.html:
          // FIXME:
          // return 'html';
          break;
        case IndexModuleType.unknown:
          break;
      }
    }
  }

  Widget _banner() {
    final it = controller.banners;
    return Background(
      alignment: Alignment.bottomCenter,
      background: CarouselSlider.builder(
        itemCount: it.length,
        itemBuilder: (BuildContext context, int index, int realIndex) {
          final data = it.elementAt(index);
          return ThumbnailImage(
            data.thumbnail,
            onTap:
                data.url != null ? () => _onPressed(data.url ?? Url()) : null,
          );
        },
        options: CarouselOptions(
          // height: 245.dh,
          // viewportFraction: 1,
          autoPlay: true,
          autoPlayInterval: const Duration(seconds: 5),
          autoPlayAnimationDuration: const Duration(milliseconds: 800),
          // autoPlayCurve: Curves.fastOutSlowIn,
          // enlargeCenterPage: true,
          // onPageChanged: (index, reason) {
          //   controller.current = index;
          // },
          aspectRatio: 980.0 / 645.0,
          // viewportFraction: 0.9999,
          viewportFraction: 1,
          initialPage: 0,
          enableInfiniteScroll: true,
          reverse: false,
          // autoPlay: true,
          // autoPlayInterval: Duration(seconds: 3),
          // autoPlayAnimationDuration: Duration(milliseconds: 800),
          autoPlayCurve: Curves.fastOutSlowIn,
          // enlargeCenterPage: true,
          // enlargeFactor: 0.3,
          onPageChanged: (index, reason) {
            controller.bannerIndex = index;
          },
          scrollDirection: Axis.horizontal,
        ),
      ),
      child: Obx(() {
        return DotsIndicator(
          dotsCount: it.length,
          position: controller.bannerIndex,
        );
      }),
    );
  }

  /// /promotion/210
  /// /category/21/1
  /// /product/68503
  /// /content {"contentMain":"1026/display","url": "https://m.efshop.com.tw/app_access?redirect=content/1026/display"}
  void _onPressed(Url url) {
    controller.talker.info(url.uri.toString());
    Get.launchUrl(url.uri);
  }

  PreferredSizeWidget? _preferredSizeWidget() {
    if (controller.tabs.isEmpty) {
      return null;
    }
    return PreferredSize(
      preferredSize: const Size.fromHeight(Constants.tabHeight),
      child: Align(
        alignment: Alignment.centerLeft,
        child: _tabBar(),
      ),
    );
  }

  PreferredSizeWidget _tabBar() {
    return TabBar(
      controller: controller.tabController,
      isScrollable: true,
      tabs: _tabs().toList(growable: false),
      padding: EdgeInsets.zero,
      labelPadding: EdgeInsets.zero,
      onTap: (value) {
        try {
          controller.pageController.jumpToPage(value);
        } catch (e) {
          controller.talker.error(e);
        }
      },
    );
  }

  Iterable<Widget> _tabs() {
    return controller.tabs.map((entry) {
      return Tab(
        iconMargin: const EdgeInsets.only(
          bottom: 2,
        ),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            minWidth: 74.dw,
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.dw),
            child: Text(
              entry.displayName,
              softWrap: false,
              textAlign: TextAlign.center,
              overflow: TextOverflow.visible,
              style: entry.textStyle,
            ),
          ),
        ),
      );
    });
  }

  Iterable<Widget> _actions(BuildContext context) sync* {
    yield Obx(() {
      return Visibility(
        visible: controller.prefProvider.devtool,
        child: ActionButton(
          onPressed: () {
            Get.toNamed(Routes.DEVTOOL);
          },
          icon: const Icon(
            Icons.logo_dev,
            size: 20,
          ),
          text: Text(
            '開發者',
            style: TextStyle(
              fontSize: 12.dsp,
              color: EfColors.grayTextDark,
              fontWeight: FontWeight.w300,
            ),
            textAlign: TextAlign.center,
            softWrap: false,
          ),
          iconMargin: EdgeInsets.only(
            bottom: 2.dh,
          ),
        ),
      );
    });
    yield ActionButton.top(
      onPressed: () {
        showSearch(
          context: context,
          delegate: EfSearchDelegate(
            wabowProvider: Get.find(),
          ),
        );
      },
      icon: 'assets/images/action_search.svg',
      text: '搜尋',
    ).sizedBox(width: 50.dw);
    // yield ActionButton.top(
    //   onPressed: () {
    //     if (changeTab != null) {
    //       changeTab!(IndexTab.profile);
    //     }
    //   },
    //   icon: 'assets/images/action_profile.svg',
    //   text: '會員',
    // ).sizedBox(width: 50.dw);
    // yield ActionButton.top(
    //   onPressed: () {
    //     if (changeTab != null) {
    //       changeTab!(IndexTab.cart);
    //     }
    //   },
    //   icon: 'assets/images/action_cart.svg',
    //   text: '結帳',
    // ).sizedBox(width: 50.dw);
    yield Obx(() {
      return ActionButton.top(
        onPressed: () async {
          final res = await EfMenuView().sheet(
            // useRootNavigator: true,
            // enableDrag: false,
            isScrollControlled: true,
            ignoreSafeArea: false,
          );
          if (res != null && res is IndexTab) {
            changeTab?.call(res);
          }
        },
        icon: 'assets/images/action_menu.svg',
        text: '更多',
        badgeCount: controller.prefProvider.unreadRes.allUnreadCount,
      ).sizedBox(width: 50.dw);
    });
    yield SizedBox(width: 12.dw);
  }

  void _scrollToAnchor(GlobalKey key) {
    final dy = key.getOffset(offset: const Offset(0, 150)).dy;
    // controller.talker.info('dy: $dy');
    controller.scroll.animateTo(
      dy,
      duration: const Duration(seconds: 1),
      curve: Curves.easeInOut,
    );
  }

  static double _getScrollOffset(GlobalKey key) {
    final renderBox = key.currentContext?.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);
    return position.dy;
  }
}
