import 'package:efshop/app/modules/search_result/controllers/search_result_controller.dart';
import 'package:efshop/app/modules/search_result/views/search_result_view.dart';
import 'package:efshop/app/modules/search_suggestion/controllers/search_suggestion_controller.dart';
import 'package:efshop/app/modules/search_suggestion/views/search_suggestion_view.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';

class EfSearchDelegate extends SearchDelegate<String> {
  final WabowProvider wabowProvider;
  Talker get talker => wabowProvider.talker;
  BoxProvider get boxProvider => wabowProvider.boxProvider;
  final SearchSuggestionController suggestion;
  final SearchResultController result;
  bool showResult;

  EfSearchDelegate({
    required this.wabowProvider,
    this.showResult = false,
  })  : suggestion = SearchSuggestionController(wabowProvider: wabowProvider),
        result = Get.put(
          SearchResultController(wabowProvider: wabowProvider),
          permanent: true,
        );

  @override
  List<Widget>? buildActions(BuildContext context) {
    Iterable<Widget> children() sync* {
      yield IconButton(
        onPressed: () {
          query = '';
          showResult = false;
          showSuggestions(context);
        },
        icon: const Icon(Icons.clear),
      );
    }

    return children().toList(growable: false);
  }

  @override
  Widget? buildLeading(BuildContext context) {
    return BackButton(
      onPressed: () {
        // if (query.isNotEmpty) {
        //   query = '';
        //   showSuggestions(context);
        //   return;
        // }
        close(context, '');
      },
    );
  }

  Future<void> _addSearchHistory(String query) async {
    final box = boxProvider.getGsBox(Boxes.setting.name, withNamespace: false);
    final ls = List<String>.from(box.read(Keys.history) ?? []);
    ls.add(query);
    final res = ls.toSet().toList(growable: false);
    box.write(Keys.history, res);
    try {
      await wabowProvider.postMembersSearchHistories(query);
    } catch (e) {
      talker.error(e);
    }
  }

  ///
  /// GA: log search
  ///
  Future<void> _logSearch(String query) async {
    try {
      await FirebaseAnalytics.instance.logSearch(
        searchTerm: query,
      );
    } catch (e) {
      talker.error(e);
    }
  }

  @override
  Widget buildResults(BuildContext context) {
    if (query.isNotEmpty && query != result.query) {
      talker.info('query: $query, result.query: ${result.query}');
      // 新增搜尋紀錄
      _addSearchHistory(query);
      // GA: log search
      _logSearch(query);
      // GA: log view search results
      // _logViewSearchResults(query);
      result.query = query;
    }
    return SearchResultView(
      onTap: (value) {
        // query = value;
        // showResults(context);
        final url = value.url;
        if (url != null) {
          // 改成用 Get.launchUrl
          Get.launchUrl(url.uri);
        } else {
          query = value.name ?? '';
          showResults(context);
        }
      },
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    talker.info('query: $query');
    if (query.isNotEmpty && query != suggestion.query) {
      talker.info('query: $query, suggestion.query: ${suggestion.query}');
      suggestion.query = query;
    }
    if (showResult) {
      Future(() => showResults(context));
    }
    return SearchSuggestionView(
      key: const PageStorageKey('SearchResultGridView'),
      ctrl: suggestion,
      onTap: (value) {
        final url = value.url;
        if (url != null) {
          // 改成用 Get.launchUrl
          Get.launchUrl(url.uri);
        } else {
          query = value.name ?? '';
          showResults(context);
        }
      },
    );
  }
}
