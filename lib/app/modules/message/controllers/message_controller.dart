import 'package:efshop/app/models/members_messages_summary.dart';
import 'package:efshop/app/models/message_data.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/enums.dart';
import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';

class MessageController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  Talker get talker => wabowProvider.talker;
  PrefProvider get prefProvider => wabowProvider.prefProvider;
  BoxProvider get boxProvider => prefProvider.boxProvider;

  final _data = <MessageData>[].obs;

  Iterable<MessageData> get data sync* {
    final res = prefProvider.unreadRes;
    final json = res.toJson();
    for (var element in _data) {
      element.badgeCount = num.tryParse('${json[element.id]}') ?? 0;
      if (Constants.authenticatedPath.contains(element.path)) {
        if (prefProvider.isLogin) {
          yield element;
        }
      } else {
        yield element;
      }
    }
  }

  MessageController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  // Future<void> _fetchNotification() async {
  //   final it = await wabowProvider.getMembersMessagesOrders();
  //   final box = boxProvider.getGsBox(Boxes.notification.name);
  //   box.erase();
  //   for (final e in it) {
  //     box.write(e.title ?? '', e.toJson());
  //   }
  //   // final item = data
  //   //     .firstWhere((element) => element.id == '${MessageType.orders.index}');
  //   // item.subtitleText = it.isNotEmpty ? it.first.title : '';
  //   // data.refresh();
  // }

  // Future<void> _fetchActivity() async {
  //   final it = await wabowProvider.getMembersMessagesActivities();
  //   // save to local storage
  //   final box = boxProvider.getGsBox(Boxes.activity.name);
  //   box.erase();
  //   for (final e in it) {
  //     box.write(e.title ?? '', e.toJson());
  //   }
  //   // final item = data.firstWhere(
  //   //     (element) => element.id == '${MessageType.activities.index}');
  //   // item.subtitleText = it.isNotEmpty ? it.first.title : '';
  //   // data.refresh();
  // }

  Future<void> _fetchSummary() async {
    try {
      final res = await wabowProvider.getMembersMessagesSummarys();
      for (var e1 in _data) {
        final r = res.firstWhere((e2) => e1.titleText == e2.title, orElse: () {
          return MembersMessagesSummary(
            message: e1.titleText,
          );
        });
        e1.subtitleText = r.message;
      }
      _data.refresh();
    } catch (e) {
      talker.error(e);
    }
  }

  Future<void> _fetchUnread() async {
    try {
      final res = await wabowProvider.getMembersMessagesUnread();
      if (res.status == true) {
        final json = res.toJson();
        for (var e in _data) {
          e.badgeCount = num.tryParse('${json[e.id]}') ?? 0;
        }
        _data.refresh();
      }
    } catch (e) {
      talker.error(e);
    }
  }

  Future<void> onRefresh() async {
    try {
      final it = MessageType.values.map((e) {
        return MessageData(
          // id: e.index.toString(),
          id: e.value,
          iconPath: e.iconPath,
          titleText: e.display,
          subtitleText: e.subtitle,
          path: e.path,
        );
      });
      _data.assignAll(it);
      // _fetchNotification();
      // _fetchActivity();
      _fetchSummary();
      _fetchUnread();
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }
}
