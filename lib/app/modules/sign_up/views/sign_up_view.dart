import 'package:efshop/app/components/background.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';

import '../controllers/sign_up_controller.dart';

class SignUpView extends GetView<SignUpController> {
  const SignUpView({super.key});
  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: SignUpController(
        wabowProvider: Get.find(),
      ),
      builder: (controller) {
        return Scaffold(
          backgroundColor: Colors.white,
          resizeToAvoidBottomInset: false,
          appBar: AppBar(
            elevation: 0,
            title: Text(
              '會員註冊',
              style: TextStyle(
                fontSize: 16.dsp,
                color: EfColors.gray,
              ),
              textAlign: TextAlign.center,
            ),
            centerTitle: true,
            leading: IconButton(
              onPressed: () {
                Get.back();
              },
              // icon: const Icon(Icons.close),
              icon: SvgPicture.asset(
                'assets/images/close.svg',
                width: 30.dw,
                height: 30.dh,
              ),
            ),
          ),
          body: Padding(
            padding: EdgeInsets.symmetric(
              // vertical: 14.dh,
              horizontal: 24.dw,
            ),
            // child: ListView(
            //   children: _children().toList(growable: false),
            // ),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: _children().toList(growable: false),
              ),
            ),
          ),
        );
      },
    );
  }

  Iterable<Widget> _children() sync* {
    // 姓名
    yield SizedBox(height: 16.dh);
    yield Background(
      background: Align(
        alignment: Alignment.centerRight,
        child: Obx(() {
          return Visibility(
            visible: controller.draft.fullname?.isEmpty ?? true,
            child: Text(
              '姓名',
              style: TextStyle(
                fontSize: Constants.buttonFontSize.dsp,
                color: EfColors.grayTextLight,
              ),
              textAlign: TextAlign.right,
            ).paddingOnly(right: 16.dw),
          );
        }),
      ),
      child: TextFormField(
        onChanged: (value) {
          controller.draft.fullname = value;
          controller.refreshDraft();
        },
        // decoration: const InputDecoration(
        //   labelText: '密碼',
        //   hintText: '姓名',
        //   suffixIcon: IconButton(
        //     onPressed: () {},
        //     icon: SvgPicture.asset(
        //       'assets/images/eye.svg',
        //       width: 20.dw,
        //       height: 20.dh,
        //     ),
        //   ),
        // ),
      ).sizedBox(height: Constants.buttonHeight.dh),
    );
    // 密碼
    yield SizedBox(height: 16.dh);
    yield Background(
      background: Align(
        alignment: Alignment.centerRight,
        child: Padding(
          padding: EdgeInsets.only(right: 24.dw),
          child: Obx(() {
            return Visibility(
              visible: controller.draft.password?.isEmpty ?? true,
              child: Text(
                '密碼',
                style: TextStyle(
                  fontSize: Constants.buttonFontSize.dsp,
                  color: EfColors.grayTextLight,
                ),
                textAlign: TextAlign.right,
              ).paddingOnly(right: 16.dw),
            );
          }),
        ),
      ),
      child: Obx(() {
        return TextFormField(
          obscureText: controller.isPasswordVisible ? false : true,
          onChanged: (value) {
            controller.draft.password = value;
            controller.refreshDraft();
          },
          // decoration: const InputDecoration(
          //   labelText: '密碼',
          //   hintText: '密碼',
          //   suffixIcon: IconButton(
          //     onPressed: () {},
          //     icon: SvgPicture.asset(
          //       'assets/images/eye.svg',
          //       width: 20.dw,
          //       height: 20.dh,
          //     ),
          //   ),
          // ),
          decoration: InputDecoration(
            suffixIcon: IconButton(
              onPressed: () => controller.togglePasswordVisibility(),
              icon: Icon(
                controller.isPasswordVisible
                    ? Icons.visibility
                    : Icons.visibility_off,
                color: EfColors.gray60,
              ),
            ),
          ),
        ).sizedBox(height: Constants.buttonHeight.dh);
      }),
    );
    // 再次輸入密碼
    yield SizedBox(height: 16.dh);
    yield Background(
      background: Align(
        alignment: Alignment.centerRight,
        child: Padding(
          padding: EdgeInsets.only(right: 24.dw),
          child: Obx(() {
            return Visibility(
              visible: controller.confirmPassword.isEmpty,
              child: Text(
                '再次輸入密碼',
                style: TextStyle(
                  fontSize: Constants.buttonFontSize.dsp,
                  color: EfColors.grayTextLight,
                ),
                textAlign: TextAlign.right,
              ).paddingOnly(right: 16.dw),
            );
          }),
        ),
      ),
      child: Obx(() {
        return TextFormField(
          obscureText: controller.isConfirmPasswordVisible ? false : true,
          onChanged: (value) {
            controller.confirmPassword = value;
            controller.refreshDraft();
          },
          decoration: InputDecoration(
            suffixIcon: IconButton(
              onPressed: () => controller.toggleConfirmPasswordVisibility(),
              icon: Icon(
                controller.isConfirmPasswordVisible
                    ? Icons.visibility
                    : Icons.visibility_off,
                color: EfColors.gray60,
              ),
            ),
          ),
        ).sizedBox(height: Constants.buttonHeight.dh);
      }),
    );
    // gender
    yield SizedBox(height: 8.dh);
    yield _genderWidget();
    yield SizedBox(height: 8.dh);
    yield Obx(() => _dateWidget());
    // checkbox
    // yield SizedBox(height: 8.dh);
    yield Row(
      children: [
        Transform.translate(
          offset: Offset(-12.dw, 0),
          child: Obx(() {
            return Checkbox(
              value: controller.agree,
              onChanged: (value) => controller.agree = value ?? false,
            );
          }),
        ),
        TextButton(
          onPressed: () {
            // 前往會員條款
            Get.toNamed(Routes.EF_WEB, parameters: {
              Keys.url: Constants.uriPrivacy.toString(),
              Keys.title: '會員條款',
            });
          },
          child: const Text(
            '已閱讀並同意衣芙會員服務條款',
            style: TextStyle(
              fontSize: 14,
              color: EfColors.gray,
            ),
            softWrap: false,
          ),
        ),
      ],
    );
    yield SizedBox(height: 12.dh);
    yield Obx(() {
      return ElevatedButton(
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.zero,
          backgroundColor: EfColors.primary,
          disabledBackgroundColor: EfColors.grayD5,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(
              Radius.circular(4.0),
            ),
          ),
        ),
        onPressed: controller.isValidate() ? _onNextPressed : null,
        child: Text(
          '送出',
          style: TextStyle(
            fontSize: Constants.buttonFontSize.dsp,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
      ).sizedBox(width: double.infinity, height: Constants.buttonHeight.dh);
    });
    yield SizedBox(height: 12.dh);
    yield Text(
      // '<EMAIL>',
      controller.draft.email ?? '',
      style: TextStyle(
        fontSize: 16.dsp,
        color: EfColors.grayD5,
      ),
      textAlign: TextAlign.right,
      softWrap: false,
    );
  }

  Widget _dateWidget() {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: const Text(
        '生日',
        style: TextStyle(
          fontSize: 14,
          color: EfColors.gray,
        ),
      ),
      title: Visibility(
        visible: controller.draft.birthdayAsString.isNotEmpty,
        replacement: const Text(
          'YYYY / MM/ DD',
          style: TextStyle(
            fontSize: 14,
            color: EfColors.gray,
          ),
        ),
        child: Text(
          controller.draft.birthdayAsString,
          style: const TextStyle(
            fontSize: 14,
            color: EfColors.gray,
          ),
        ),
      ),
      onTap: () async {
        try {
          final res = await showDatePicker(
            context: Get.context!,
            initialDate: DateTime.now(),
            firstDate: DateTime(1900),
            lastDate: DateTime.now(),
          );
          if (res != null) {
            controller.draft.birthday = res.toDateString();
            controller.refreshDraft();
          }
        } catch (e) {
          controller.talker.error(e);
        }
      },
    );
    // return Row(
    //   children: [
    //     const Text(
    //       '生日',
    //       style: TextStyle(
    //         fontSize: 14,
    //         color: EfColors.gray,
    //       ),
    //     ),
    //     const SizedBox(width: 16),
    //     // Expanded(
    //     //   child: TextFormField(
    //     //     onChanged: (value) {
    //     //       // controller.draft.birthday = value;
    //     //       controller.refreshDraft();
    //     //     },
    //     //     decoration: const InputDecoration(
    //     //       hintText: 'YYYY/MM/DD',
    //     //     ),
    //     //   ),
    //     // ),
    //     TextFormField(
    //       initialValue: controller.yyyy,
    //       onChanged: (value) {
    //         controller.yyyy = value;
    //         controller.refreshDraft();
    //       },
    //       textAlign: TextAlign.center,
    //       decoration: const InputDecoration(
    //         hintText: 'YYYY',
    //         contentPadding: EdgeInsets.symmetric(horizontal: 2),
    //       ),
    //       keyboardType: TextInputType.number,
    //     ).sizedBox(width: 65, height: Constants.widgetHeight.dh),
    //     const SizedBox(
    //       width: 16,
    //       child: Center(
    //         child: Text(
    //           '/',
    //           style: TextStyle(
    //             fontSize: 14,
    //             color: EfColors.border,
    //           ),
    //         ),
    //       ),
    //     ),
    //     TextFormField(
    //       initialValue: controller.mm,
    //       textAlign: TextAlign.center,
    //       onChanged: (value) {
    //         controller.mm = value;
    //         controller.refreshDraft();
    //       },
    //       decoration: const InputDecoration(
    //         hintText: 'MM',
    //         contentPadding: EdgeInsets.symmetric(horizontal: 2),
    //       ),
    //       keyboardType: TextInputType.number,
    //     ).sizedBox(width: 65, height: Constants.widgetHeight.dh),
    //     const SizedBox(
    //       width: 16,
    //       child: Center(
    //         child: Text(
    //           '/',
    //           style: TextStyle(
    //             fontSize: 14,
    //             color: EfColors.border,
    //           ),
    //         ),
    //       ),
    //     ),
    //     TextFormField(
    //       textAlign: TextAlign.center,
    //       onChanged: (value) {
    //         controller.dd = value;
    //         controller.refreshDraft();
    //       },
    //       decoration: const InputDecoration(
    //         hintText: 'DD',
    //         contentPadding: EdgeInsets.symmetric(horizontal: 2),
    //       ),
    //       keyboardType: TextInputType.number,
    //     ).sizedBox(width: 65, height: Constants.widgetHeight.dh),
    //   ],
    // );
  }

  Widget _genderWidget() {
    return Row(
      children: [
        const Text(
          '性別',
          style: TextStyle(
            fontSize: 14,
            color: EfColors.gray,
          ),
        ),
        const SizedBox(width: 16),
        Obx(() {
          final isSelected = controller.gender.isFemale;
          return TextButton(
            style: TextButton.styleFrom(
              backgroundColor:
                  isSelected ? EfColors.grayLight : Colors.transparent,
              side: isSelected
                  ? null
                  : const BorderSide(
                      color: EfColors.border,
                      width: 1,
                    ),
              shape: const RoundedRectangleBorder(
                borderRadius: Constants.buttonBorderRadius,
              ),
            ),
            onPressed: () {
              controller.gender = Gender.female;
              controller.refreshDraft();
            },
            child: Text(
              Gender.female.display,
              style: TextStyle(
                // fontSize: 14,
                color: isSelected ? Colors.white : EfColors.gray,
              ),
            ),
          );
        }),
        SizedBox(width: 16.dh),
        Obx(() {
          final isSelected = controller.gender.isMale;
          return TextButton(
            style: TextButton.styleFrom(
              backgroundColor:
                  isSelected ? EfColors.grayLight : Colors.transparent,
              side: isSelected
                  ? null
                  : const BorderSide(
                      color: EfColors.border,
                      width: 1,
                    ),
              shape: const RoundedRectangleBorder(
                borderRadius: Constants.buttonBorderRadius,
              ),
            ),
            onPressed: () {
              controller.gender = Gender.male;
              controller.refreshDraft();
            },
            child: Text(
              Gender.male.display,
              style: TextStyle(
                color: isSelected ? Colors.white : EfColors.gray,
              ),
            ),
          );
        }),
      ],
    );
  }

  Future<void> _onNextPressed() async {
    Get.showLoading();
    try {
      final res = await controller.signUp();
      // 成功，關閉 Loading
      Get.back();
      // 關閉所頁面，返回參數
      Get.back(
        result: res,
        id: SubRouteType.signIn.index,
      );
    } catch (e) {
      Get.back();
      // Get.showAlert(e.toString());
      const errorMessage = '密碼未符合規則\n[1]至少6位數\n[2]必須同時包含英數字\n[3]不得出現在帳號之中';
      Get.showAlert(errorMessage);
    }
  }
}
