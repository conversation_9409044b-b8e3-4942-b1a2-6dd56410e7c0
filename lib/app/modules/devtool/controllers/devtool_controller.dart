import 'package:device_info_plus/device_info_plus.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';

class DevtoolController extends GetxController with StateMixin<String> {
  final PrefProvider prefProvider;
  Talker get talker => prefProvider.talker;
  late Rx<BaseDeviceInfo> _deviceInfo;
  BaseDeviceInfo get deviceInfo => _deviceInfo.value;

  DevtoolController({
    required this.prefProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      final dInfo = await prefProvider.deviceInfo.deviceInfo;
      _deviceInfo = dInfo.obs;
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }
}
