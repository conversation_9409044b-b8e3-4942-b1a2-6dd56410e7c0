import 'dart:async';

import 'package:efshop/app/models/category.dart';
import 'package:efshop/app/models/product_detail.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';

class SearchResultController extends GetxController with StateMixin<String> {
  final _disposable = Completer();
  final WabowProvider wabowProvider;
  Talker get talker => wabowProvider.talker;
  final _query = ''.obs;
  String get query => _query.value;
  set query(String value) {
    if (_query.value != value) {
      _query.value = value;
      change('', status: RxStatus.loading());
      onRefresh();
    }
  }

  final data = <ProductDetail>[].obs;
  final keywords = <Category>[].obs;

  SearchResultController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
    // _query.stream
    //     .distinct()
    //     .tap((p0) => change('', status: RxStatus.loading()))
    //     .debounce(const Duration(milliseconds: 500))
    //     .asyncMap((event) => onRefresh())
    //     .takeUntil(_disposable.future)
    //     .listen((event) {});
  }

  @override
  void onReady() {
    super.onReady();
    // onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      final res1 = await wabowProvider.getSearchProducts(query);
      data.assignAll(res1);
      await _logViewSearchResults(query);
      // final res2 = await wabowProvider.getTagProducts(query);
      // data.assignAll([...res1, ...res2]);
      if (data.isEmpty) {
        final res = await wabowProvider.getHotKeyword();
        keywords.assignAll(res);
      }
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  ///
  /// GA: log view search results
  ///
  Future<void> _logViewSearchResults(String query) async {
    try {
      await FirebaseAnalytics.instance.logViewSearchResults(
        searchTerm: query,
      );
    } catch (e) {
      talker.error(e);
    }
  }
}
