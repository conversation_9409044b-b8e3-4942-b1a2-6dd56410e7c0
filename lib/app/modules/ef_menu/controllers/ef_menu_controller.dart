import 'package:efshop/app/models/menu_res.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/result.dart';
import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

class EfMenuController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  Talker get talker => wabowProvider.talker;
  PrefProvider get prefProvider => wabowProvider.prefProvider;

  final _isLogin = false.obs;
  bool get isLogin => _isLogin.value;

  // 漢堡選單
  final _menus = <MenuRes>[].obs;
  Iterable<MenuRes> get menus sync* {
    for (var element in _menus) {
      yield* element.menus;
    }
  }

  late final WebViewController webViewController;
  final _scrollHeight = 1.0.obs;
  double get scrollHeight => _scrollHeight.value;

  EfMenuController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _isLogin.value = prefProvider.isLogin;
    _initWebViewController();
    onRefresh();
  }

  void _initWebViewController() {
    late final PlatformWebViewControllerCreationParams params;
    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }
    webViewController = WebViewController.fromPlatformCreationParams(params);
    if (webViewController.platform is AndroidWebViewController) {
      // AndroidWebViewController.enableDebugging(true);
      (webViewController.platform as AndroidWebViewController)
          .setMediaPlaybackRequiresUserGesture(false);
    }
    webViewController.setJavaScriptMode(JavaScriptMode.unrestricted);
    webViewController.setNavigationDelegate(NavigationDelegate(
      onPageFinished: (url) async {
        talker.info('onPageFinished: $url');
        final result = await webViewController.runJavaScriptReturningResult(
            'document.documentElement.scrollHeight;');
        talker.info('onPageFinished: $result');
        // 将结果解析为双精度数值
        _scrollHeight.value = (result as num).toDouble();
      },
    ));
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<Result<String, Exception>> _fetchFooter() async {
    try {
      final res = await wabowProvider.getFooter();
      // <div style="background-color: #ccc; color: white; padding: 1rem 1rem; line-height: 1.8rem;">
      // <p>消費者服務 9：00 ~ 17：30 ( 週休及例假日除外 )</p>
      // <p>客服專線 / (02) 2602-0707</p>
      // <p>聯絡我們 / <EMAIL></p>
      // </div>
      await webViewController.loadHtmlString(res.toNotoHtml);
      // return Success(res);
      return Result.success(res);
    } on Exception catch (e) {
      // return Failure(e);
      return Result.failure(e);
    }
  }

  Future<void> onRefresh() async {
    try {
      // 取得漢堡選單
      final res = await wabowProvider.getMenus();
      _menus.assignAll(res);
      change('', status: RxStatus.success());
      _fetchFooter();
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  void refreshMenus() {
    _menus.refresh();
  }
}
