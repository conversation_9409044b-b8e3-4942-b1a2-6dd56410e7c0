import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';

import '../controllers/password_editor_controller.dart';

class PasswordEditorView extends GetView<PasswordEditorController> {
  const PasswordEditorView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('變更密碼'),
        centerTitle: true,
        actions: _actions().toList(growable: false),
      ),
      body: _body(),
    );
  }

  Iterable<Widget> _actions() sync* {
    yield TextButton(
      onPressed: _submit,
      child: const Text(
        '儲存',
        style: TextStyle(
          fontSize: 15,
          color: EfColors.gray98,
        ),
        softWrap: false,
      ),
    );
  }

  Widget _body() {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: _children().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield _password();
    yield const Divider(height: 1);
    yield _newPassword();
    yield const Divider(height: 1);
    yield _confirmPassword();
  }

  Widget _password() {
    return ListTile(
      title: TextFormField(
        focusNode: controller.oldPasswordFocusNode,
        controller: controller.oldPasswordEditing,
        decoration: const InputDecoration(
          contentPadding: EdgeInsets.zero,
          hintText: '請輸入舊密碼',
          border: InputBorder.none,
          focusedBorder: InputBorder.none,
          enabledBorder: InputBorder.none,
          errorBorder: InputBorder.none,
        ),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return '舊密碼是必填欄位';
          }
          return null;
        },
        onChanged: (value) {
          controller.talker.info('onChanged: $value');
          // 監控使用者輸入的文字
          controller.draft.password = value;
          controller.refreshDraft();
        },
      ),
      trailing: Obx(() {
        final password = controller.draft.password;
        if (password == null || password.isEmpty) {
          return const SizedBox.shrink();
        }
        if (!controller.oldPasswordFocusNode.hasFocus) {
          return const SizedBox.shrink();
        }
        return IconButton(
          padding: EdgeInsets.zero,
          onPressed: () {
            controller.talker.info('onPressed');
            // 清除輸入欄位的文字
            controller.oldPasswordEditing.clear();
            controller.refreshDraft();
          },
          icon: const Icon(Icons.cancel),
        );
      }),
    );
  }

  Widget _newPassword() {
    return ListTile(
      title: TextFormField(
        focusNode: controller.newPasswordFocusNode,
        controller: controller.newPasswordEditing,
        decoration: const InputDecoration(
          contentPadding: EdgeInsets.zero,
          hintText: '請輸入新密碼',
          border: InputBorder.none,
          focusedBorder: InputBorder.none,
          enabledBorder: InputBorder.none,
          errorBorder: InputBorder.none,
        ),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return '新密碼是必填欄位';
          }
          return null;
        },
        onChanged: (value) {
          controller.talker.info('onChanged: $value');
          // 監控使用者輸入的文字
          controller.draft.newPassword = value;
          controller.refreshDraft();
        },
      ),
      trailing: Obx(() {
        final password = controller.draft.newPassword;
        if (password == null || password.isEmpty) {
          return const SizedBox.shrink();
        }
        if (!controller.newPasswordFocusNode.hasFocus) {
          return const SizedBox.shrink();
        }
        return IconButton(
          padding: EdgeInsets.zero,
          onPressed: () {
            controller.talker.info('onPressed');
            // 清除輸入欄位的文字
            controller.newPasswordEditing.clear();
            controller.refreshDraft();
          },
          icon: const Icon(Icons.cancel),
        );
      }),
    );
  }

  Widget _confirmPassword() {
    return ListTile(
      title: TextFormField(
        focusNode: controller.confirmPasswordFocusNode,
        controller: controller.confirmPasswordEditing,
        decoration: const InputDecoration(
          contentPadding: EdgeInsets.zero,
          hintText: '請再次輸入新密碼',
          border: InputBorder.none,
          focusedBorder: InputBorder.none,
          enabledBorder: InputBorder.none,
          errorBorder: InputBorder.none,
        ),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return '確認新密碼是必填欄位';
          }
          return null;
        },
        onChanged: (value) {
          controller.talker.info('onChanged: $value');
          // 監控使用者輸入的文字
          controller.refreshDraft();
        },
      ),
      trailing: Obx(() {
        final password = controller.draft.newPassword; // for obx
        if (controller.confirmPasswordEditing.text.isEmpty) {
          return const SizedBox.shrink();
        }
        if (!controller.confirmPasswordFocusNode.hasFocus) {
          return const SizedBox.shrink();
        }
        return IconButton(
          padding: EdgeInsets.zero,
          onPressed: () {
            controller.talker.info('onPressed');
            // 清除輸入欄位的文字
            controller.confirmPasswordEditing.clear();
            controller.refreshDraft();
          },
          icon: const Icon(Icons.cancel),
        );
      }),
      // trailing: IconButton(
      //   padding: EdgeInsets.zero,
      //   onPressed: () {
      //     controller.talker.info('onPressed');
      //     // 清除輸入欄位的文字
      //     controller.confirmPasswordEditing.clear();
      //     controller.refreshDraft();
      //   },
      //   icon: const Icon(Icons.cancel),
      // ),
    );
  }

  Future<void> _submit() async {
    try {
      Get.showLoading();
      await controller.submit();
      // hide loading
      Get.back();
      //
      Get.back();
    } catch (e) {
      // hide loading
      Get.back();
      Get.showAlert(e.toString());
    }
  }
}
