import 'package:efshop/app/models/members_messages_order.dart';
import 'package:efshop/app/providers/message_provider.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/error_type.dart';
import 'package:get/get.dart';

class NotificationController extends GetxController with StateMixin<String> {
  final MessageProvider messageProvider;
  WabowProvider get wabowProvider => messageProvider.wabowProvider;
  PrefProvider get prefProvider => wabowProvider.prefProvider;
  // BoxProvider get boxProvider => wabowProvider.boxProvider;
  final data = <MembersMessagesOrder>[].obs;

  NotificationController({
    required this.messageProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  // Iterable<MembersMessagesOrder> get data sync* {
  //   final box = boxProvider.getGsBox(Boxes.notification.name);
  //   for (dynamic json in box.getValues()) {
  //     yield MembersMessagesOrder.fromJson(json);
  //   }
  // }

  Future<void> onRefresh() async {
    try {
      if (prefProvider.isNotLogin) {
        throw ErrorType.unauthorized;
      }
      messageProvider.markAsRead(MessageType.orders);
      final res = await wabowProvider.getMembersMessagesOrders();
      data.assignAll(res);
      change('', status: data.isEmpty ? RxStatus.empty() : RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }
}
