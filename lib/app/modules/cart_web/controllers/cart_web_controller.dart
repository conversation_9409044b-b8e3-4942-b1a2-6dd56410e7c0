import 'dart:async';
import 'dart:io';

import 'package:efshop/app/models/appier_cart_viewed_res.dart';
import 'package:efshop/app/models/cart_items_res.dart';
import 'package:efshop/app/providers/appier_provider.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

class CartWebController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  Talker get talker => wabowProvider.talker;
  PrefProvider get prefProvider => wabowProvider.prefProvider;
  AppierProvider get appierProvider => wabowProvider.appierProvider;
  // web view controller
  late final WebViewController webViewController;
  final _disposable = Completer();
  // url
  final _url = ''.obs;
  String get url => _url.value;
  set url(String value) => _url.value = value;
  // order id
  final _orderId = ''.obs;
  String get orderId => _orderId.value;
  set orderId(String value) => _orderId.value = value;
  // change cart tab
  final AsyncValueSetter<IndexTab>? changeTab;
  final _linePayTriggered = false.obs;
  // cart items
  late CartItemsRes cartItems;
  final _toggle = false.obs;
  bool get toggle => _toggle.value;

  CartWebController({
    required this.wabowProvider,
    this.changeTab,
  });

  @override
  void onInit() {
    super.onInit();
    _initWebViewController();
    _url.stream
        .where((event) => event.isNotEmpty)
        .asyncMap((event) => _loadFromUrl())
        .takeUntil(_disposable.future)
        .listen((event) {});
    Stream.periodic(1.seconds).takeUntil(_disposable.future).listen((event) {
      _toggle.value = !_toggle.value;
    });
  }

  Future<void> _loadFromUrl() async {
    await webViewController.loadRequest(
      Uri.parse(url),
      // 購物車頁面需要的 header
      headers: <String, String>{
        'Authorization': 'Bearer ${prefProvider.token}',
        'device_type': Platform.operatingSystem,
        'app_version': prefProvider.packageInfo.version,
      },
    );
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      cartItems = await wabowProvider.getCartItems();
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  Future<void> onBackPressed() async {
    var canGoBack = await webViewController.canGoBack();
    if (_linePayTriggered.value) {
      canGoBack = false;
    }
    if (canGoBack) {
      final currentUrl = await webViewController.currentUrl();
      // https://m.efshop.com.tw/cart
      if (currentUrl == Constants.uriCartRoot.toString()) {
        // Get.back();
        changeTab?.call(IndexTab.category);
      } else {
        await webViewController.goBack();
      }
    } else {
      // Get.back();
      changeTab?.call(IndexTab.category);
    }
  }

  void _initWebViewController() {
    late final PlatformWebViewControllerCreationParams params;
    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }
    webViewController = WebViewController.fromPlatformCreationParams(params);
    if (webViewController.platform is AndroidWebViewController) {
      // AndroidWebViewController.enableDebugging(true);
      (webViewController.platform as AndroidWebViewController)
          .setMediaPlaybackRequiresUserGesture(false);
    }
    webViewController.setJavaScriptMode(JavaScriptMode.unrestricted);
    webViewController.setOnConsoleMessage((message) {
      talker.info('onConsoleMessage: ${message.message}');
    });
    webViewController.setNavigationDelegate(NavigationDelegate(
      onNavigationRequest: (request) {
        final url = request.url;
        // const url = 'https://m.efshop.com.tw/app/CartFinish/success/7278195?openExternalBrowser=1';
        // const url = 'https://m.efshop.com.tw/app/CartFinish/fail/7422211?openExternalBrowser=1';
        // 測試用
        // scan
        // final url = 'intent://scan/#Intent;scheme=zxing;package=com.google.zxing.client.android;end';
        // line pay
        // const url =
        //     'intent://pay/payment/K3EyVks5QnZyVnd3alNsdUlDS1NkMk54dkxtaHg2Qm02dlVCTVl3RStnWExvTHZabGZYUVJHcXdSY3dzRnZHbg#Intent;scheme=line;action=android.intent.action.VIEW;category=android.intent.category.BROWSABLE;package=jp.naver.line.android;end';
        // const url = 'https://m.efshop.com.tw/showEmptyCart';
        // const url =
        //     'https://m.efshop.com.tw/CartFinish/fail/7278177?payment_name=%E4%BF%A1%E7%94%A8%E5%8D%A1%E4%BB%98%E6%AC%BE&source=android';
        // onNavigationRequest: https://m.efshop.com.tw/showEmptyCart
        // onNavigationRequest: https://m.efshop.com.tw/CartFinish/fail/7278164?payment_name=%E4%BF%A1%E7%94%A8%E5%8D%A1%E4%BB%98%E6%AC%BE&source=android
        // onNavigationRequest: https://m.efshop.com.tw/app/CartFinish/fail/7278164?openExternalBrowser=1
        // onNavigationRequest: https://epos.ctbcbank.com/mauth/SSLAuthUI.jsp?merID=38379&URLEnc=9A9297CEF93271CB0614C2DB0C26E0F5650FC865EE3912267FAA7590CB0472E6D99C9BAAE86B42D5EC834FD850DFF8F253192158C9558A61DD1D13E8E11C94FEC8AE45454C1DD438998798CC17BB68FC07003733AAF8EBEC3BABC935E7A3CB7682DCC098D74CF7A4D84D9BC095B9B6466DCD5A8BB511A2063D9A93533DEBB7548F5BBAF69BA909C813733638C2960C2388BCF6CBBE0FC75DF13FC23AF1DB74DE926F71FCFA3837620A9EF1B53B26FF885DF2A34AD8F3430E3C5332912D79E05D8772034BBEB16DEF0E3D77E516738B0E0209E8306774B8067ACCCB5A9CC78D6AD733D6C706C2BAFFC50DA6B44B06E419AD585354AB0F527D&_gl=1*cxgbzr*_ga******************************_ga_7WJCGM1SV3********************************************.
        // onNavigationRequest: https://m.efshop.com.tw/CartFinish/fail/7278177?payment_name=%E4%BF%A1%E7%94%A8%E5%8D%A1%E4%BB%98%E6%AC%BE&source=android
        // onNavigationRequest: https://m.efshop.com.tw/app/CartFinish/fail/7278177?openExternalBrowser=1
        // ---
        // onNavigationRequest: https://epos.ctbcbank.com/mauth/SSLAuthUI.jsp?merID=38379&URLEnc=9A9297CEF93271CB0614C2DB0C26E0F5650FC865EE3912267FAA7590CB0472E6D99C9BAAE86B42D5EC834FD850DFF8F294E08389EBC271CB9B6D9AF23AF31CAF8A02275868C290106643D377265F225265E736EDF13413473A69C6426E9F994F9F880A80F211B44B28B43E00DC0BA19CB0E03182F11F3E42E146C79E9696E83BD9C3175A3C809A1A6AA6D0ADE9EF07E55154A9FEF8A240495DF2D614B26BE177789A3AF3B82D91E5AA05F7AEAB98B3F0D33376B8AEA72ADC82729EFB0C000316501A3C4A2E4A85783666E95CE1CE7D609C7E57E402894709ACCFEE1B536F2F03CDAF079302985DE6F181BD8BA584B9BC025BA820DD741282&_gl=1*1607zaf*_ga******************************_ga_7WJCGM1SV3********************************************.
        // onNavigationRequest: https://epos.ctbcbank.com/mauth/SSLAuthUI.jsp?merID=38379&URLEnc=9A9297CEF93271CB0614C2DB0C26E0F5650FC865EE3912267FAA7590CB0472E6D99C9BAAE86B42D5EC834FD850DFF8F2085377AF547EA2AD3AA536FE786E14A68C996A59185BD2FC0F4D52B3DC21FE87C051B718374AACC671097BE0BF21A4E372BE5F491A1CB9504186B6FB197B60AC1B9C53E14EA12DEE9B9B211550451C23694D60B3BA38F04AB78A196773FECB6B5196E20FDA38756DC010649ECE1FACFB3C08C3E2D3FC27760571E5968AC4D5CDD893DC340AC025C9FE3334E4518815B352B4C60A9804BCABFD3515513220EE0B196AA7587F60462264E4C5181B2C3C94F1A0493D5E77D1E75A266402F22B24F87CD2D7FBB7D28159&_gl=1*fjqwrf*_ga******************************_ga_7WJCGM1SV3********************************************.
        // onNavigationRequest: https://m.efshop.com.tw/CartFinish/success/7278195?payment_name=%E4%BF%A1%E7%94%A8%E5%8D%A1%E4%BB%98%E6%AC%BE&source=android
        // onNavigationRequest: https://m.efshop.com.tw/app/CartFinish/success/7278195?openExternalBrowser=1
        // --- 點擊了商品，攔截事件並跳轉到商品頁面
        // onNavigationRequest: https://m.efshop.com.tw/showProduct/65951
        // --- line pay
        // onNavigationRequest: https://web-pay.line.me/web/payment/wait?transactionReserveId=SjRFekVqZ0puSkZJaWlTSU1PSWpzemt2TW0vZ2xyNGI2MkR5M25VN3Q5WUU4cDN4T1kvV0c4WHdxcktGb2RVUQ
        // onNavigationRequest: https://web-pay.line.me/web/payment/waitPreLogin?transactionReserveId=SjRFekVqZ0puSkZJaWlTSU1PSWpzemt2TW0vZ2xyNGI2MkR5M25VN3Q5WUU4cDN4T1kvV0c4WHdxcktGb2RVUQ
        // onNavigationRequest: intent://pay/payment/SjRFekVqZ0puSkZJaWlTSU1PSWpzemt2TW0vZ2xyNGI2MkR5M25VN3Q5WUU4cDN4T1kvV0c4WHdxcktGb2RVUQ#Intent;scheme=line;action=android.intent.action.VIEW;category=android.intent.category.BROWSABLE;package=jp.naver.line.android;end
        talker.info('onNavigationRequest: $url');
        final uri = Uri.parse(url);
        // 空購物車 https://m.efshop.com.tw/showEmptyCart
        if (uri.pathSegments.contains(Keys.showEmptyCart)) {
          prefProvider.cartQuantity = 0;
          prefProvider.cartItems = CartItemsRes();
          changeTab?.call(IndexTab.showEmptyCart);
          // 測試用
          // changeTab?.call(const MapEntry(CartTab.orderFailed, '7278195'));
          // launchUrl(Uri.parse('intent://pay/payment/SjRFekVqZ0puSkZJaWlTSU1PSWpzemt2TW0vZ2xyNGI2MkR5M25VN3Q5WUU4cDN4T1kvV0c4WHdxcktGb2RVUQ#Intent;scheme=line;action=android.intent.action.VIEW;category=android.intent.category.BROWSABLE;package=jp.naver.line.android;end'));
          // launchUrl(Uri.parse('https://web-pay.line.me/web/payment/waitPreLogin?transactionReserveId=SjRFekVqZ0puSkZJaWlTSU1PSWpzemt2TW0vZ2xyNGI2MkR5M25VN3Q5WUU4cDN4T1kvV0c4WHdxcktGb2RVUQ'));
          // launchUrl(Uri.parse('https://web-pay.line.me/web/payment/wait?transactionReserveId=SjRFekVqZ0puSkZJaWlTSU1PSWpzemt2TW0vZ2xyNGI2MkR5M25VN3Q5WUU4cDN4T1kvV0c4WHdxcktGb2RVUQ'));
          // final uri2 = Uri.parse(
          //     'https://web-pay.line.me/web/payment/wait?transactionReserveId=SjRFekVqZ0puSkZJaWlTSU1PSWpzemt2TW0vZ2xyNGI2MkR5M25VN3Q5WUU4cDN4T1kvV0c4WHdxcktGb2RVUQ');
          // final uri2 = Uri.parse(
          //     'intent://pay/payment/SjRFekVqZ0puSkZJaWlTSU1PSWpzemt2TW0vZ2xyNGI2MkR5M25VN3Q5WUU4cDN4T1kvV0c4WHdxcktGb2RVUQ#Intent;scheme=line;action=android.intent.action.VIEW;category=android.intent.category.BROWSABLE;package=jp.naver.line.android;end');
          // webViewController.loadRequest(uri2);
          // canLaunchUrl(uri2).then((value) {
          //   if (value) {
          //     launchUrl(uri2);
          //   }
          // });
          return NavigationDecision.prevent;
        }
        // 訂單結果
        if (uri.pathSegments.contains(Keys.cartFinish)) {
          orderId = uri.pathSegments.last;
          if (uri.pathSegments.contains(Keys.success)) {
            // success - https://m.efshop.com.tw/app/CartFinish/success/7278195?openExternalBrowser=1
            prefProvider.cartQuantity = 0;
            Get.parameters = {
              Keys.orderId: orderId,
              Keys.orderStatus: Keys.success,
            };
            changeTab?.call(IndexTab.appCartFinish);
            return NavigationDecision.prevent;
          } else if (uri.pathSegments.contains(Keys.fail)) {
            // fail - https://m.efshop.com.tw/app/CartFinish/fail/7278177?openExternalBrowser=1
            prefProvider.cartQuantity = 0;
            Get.parameters = {
              Keys.orderId: orderId,
              Keys.orderStatus: Keys.fail,
            };
            changeTab?.call(IndexTab.appCartFinish);
            return NavigationDecision.prevent;
          }
        }
        // line pay for iOS (在這階段才會跳轉回App)
        // https://web-pay.line.me/web/payment/wait?transactionReserveId=TWhPTDg1Q1pXSnh3L3pBcG9QTTFpdmgxZGhBdTV1Ync0cmkrVm1aTmFrZTJQOG90Y0ozVk5nSFk2MEtxRXdDSA
        if (uri.authority == Constants.linePayAuthority && GetPlatform.isIOS) {
          // https: //web-pay.line.me/web/payment/waitPreLogin?transactionReserveId=TWhPTDg1Q1pXSnh3L3pBcG9QTTFpdmgxZGhBdTV1Ync0cmkrVm1aTmFrZTJQOG90Y0ozVk5nSFk2MEtxRXdDSA
          if (uri.pathSegments.contains('waitPreLogin')) {
            if (uri.queryParameters.containsKey(Keys.transactionReserveId)) {
              final transactionReserveId =
                  uri.queryParameters[Keys.transactionReserveId];
              // 'https://line.me/R/pay/payment/$tId';
              final lineUri = Uri.https(Constants.lineMeAuthority,
                  '/R/pay/payment/$transactionReserveId');
              canLaunchUrl(lineUri).then((value) async {
                if (value) {
                  _linePayTriggered.value = await launchUrl(lineUri,
                      mode: LaunchMode.externalApplication);
                  talker.info('launchUrl: $_linePayTriggered.value');
                }
              });
              return NavigationDecision.prevent;
            }
          }
          return NavigationDecision.navigate;
        }
        // line pay for Android (在這階段才會跳轉回App)
        if (['intent', 'line'].contains(uri.scheme) && GetPlatform.isAndroid) {
          var lineUri = uri;
          if (uri.scheme == 'intent') {
            // intent://pay/payment/K3EyVks5QnZyVnd3alNsdUlDS1NkMk54dkxtaHg2Qm02dlVCTVl3RStnWExvTHZabGZYUVJHcXdSY3dzRnZHbg
            lineUri = Uri(
              scheme: 'line',
              host: uri.host,
              path: uri.path,
            );
          }
          // line://pay/payment/MkRLNzM4Q1cyZjFBMWIvZmdGTjhPb3d5b0I4eEg1RFRvVFhQbFFDTjJvVGVuUFF4QmdOYmpqQjE4SXN0Q2ZMVw
          // https://line.me/R/pay/payment/MkRLNzM4Q1cyZjFBMWIvZmdGTjhPb3d5b0I4eEg1RFRvVFhQbFFDTjJvVGVuUFF4QmdOYmpqQjE4SXN0Q2ZMVw
          final transactionReserveId = lineUri.pathSegments.last;
          // 'https://line.me/R/pay/payment/$tId';
          lineUri = Uri.https(Constants.lineMeAuthority,
              '/R/pay/payment/$transactionReserveId');
          canLaunchUrl(lineUri).then((value) async {
            if (value) {
              _linePayTriggered.value = await launchUrl(lineUri,
                  mode: LaunchMode.externalApplication);
              talker.info('launchUrl: $_linePayTriggered.value');
            }
          });
          return NavigationDecision.prevent;
        }
        // 判斷可以跳轉的 url
        if (Get.canLaunchUrl(uri) && uri.host.contains(Constants.efDomain)) {
          Get.launchUrl(uri).then((value) {
            // 重新整理 (可能有商品增加到購物車)
            // webViewController.reload(); // NOTE: 造成迴圈
            _loadFromUrl();
          });
          return NavigationDecision.prevent;
        }
        // if ('about:blank' == url) {
        //   // Future(() => Get.back());
        //   Future(() {
        //     changeTab?.call(const MapEntry(CartTab.category, ''));
        //   });
        //   return NavigationDecision.navigate;
        // }
        return NavigationDecision.navigate;
      },
      onUrlChange: (change) {
        // onUrlChange: https://m.efshop.com.tw/showEmptyCart
        // onUrlChange: https://m.efshop.com.tw/cart
        // onUrlChange: https://m.efshop.com.tw/cart_payment
        // onUrlChange: https://m.efshop.com.tw/cart_checkout
        // onUrlChange: https://epos.ctbcbank.com/mauth/SSLAuthUI.jsp?merID=38379&URLEnc=9A9297CEF93271CB0614C2DB0C26E0F5650FC865EE3912267FAA7590CB0472E6D99C9BAAE86B42D5EC834FD850DFF8F253192158C9558A61DD1D13E8E11C94FEC8AE45454C1DD438998798CC17BB68FC07003733AAF8EBEC3BABC935E7A3CB7682DCC098D74CF7A4D84D9BC095B9B6466DCD5A8BB511A2063D9A93533DEBB7548F5BBAF69BA909C813733638C2960C2388BCF6CBBE0FC75DF13FC23AF1DB74DE926F71FCFA3837620A9EF1B53B26FF885DF2A34AD8F3430E3C5332912D79E05D8772034BBEB16DEF0E3D77E516738B0E0209E8306774B8067ACCCB5A9CC78D6AD733D6C706C2BAFFC50DA6B44B06E419AD585354AB0F527D&_gl=1*cxgbzr*_ga******************************_ga_7WJCGM1SV3********************************************.
        // onUrlChange: https://epos.ctbcbank.com/mauth/authPayment
        // onUrlChange: https://epos.ctbcbank.com/mauth/3dAuth-v2
        // onUrlChange: https://m.efshop.com.tw/CartFinish/success/7278195?payment_name=%E4%BF%A1%E7%94%A8%E5%8D%A1%E4%BB%98%E6%AC%BE&source=android
        // onUrlChange: https://m.efshop.com.tw/app/CartFinish/success/7278195?openExternalBrowser=1
        // ---
        // onUrlChange: https://m.efshop.com.tw/CartFinish/fail/7278164?payment_name=%E4%BF%A1%E7%94%A8%E5%8D%A1%E4%BB%98%E6%AC%BE&source=android
        // onUrlChange: https://m.efshop.com.tw/app/CartFinish/fail/7278164?openExternalBrowser=1
        // ---
        // onUrlChange: https://m.efshop.com.tw/CartFinish/fail/7278177?payment_name=%E4%BF%A1%E7%94%A8%E5%8D%A1%E4%BB%98%E6%AC%BE&source=android
        // onUrlChange: https://m.efshop.com.tw/app/CartFinish/fail/7278177?openExternalBrowser=1
        // ---
        // onUrlChange: https://m.efshop.com.tw/showProduct/65951
        // --- 加購商品
        // onUrlChange: https://m.efshop.com.tw/promotion-addon
        // --- 活動 promotion 連結
        // onUrlChange: https://m.efshop.com.tw/promotion/212
        // --- line pay
        // onUrlChange: https://web-pay.line.me/web/payment/wait?transactionReserveId=SjRFekVqZ0puSkZJaWlTSU1PSWpzemt2TW0vZ2xyNGI2MkR5M25VN3Q5WUU4cDN4T1kvV0c4WHdxcktGb2RVUQ
        // onUrlChange: https://web-pay.line.me/web/payment/waitPreLogin?transactionReserveId=SjRFekVqZ0puSkZJaWlTSU1PSWpzemt2TW0vZ2xyNGI2MkR5M25VN3Q5WUU4cDN4T1kvV0c4WHdxcktGb2RVUQ
        // onUrlChange: intent://pay/payment/SjRFekVqZ0puSkZJaWlTSU1PSWpzemt2TW0vZ2xyNGI2MkR5M25VN3Q5WUU4cDN4T1kvV0c4WHdxcktGb2RVUQ#Intent;scheme=line;action=android.intent.action.VIEW;category=android.intent.category.BROWSABLE;package=jp.naver.line.android;end
        final url = change.url ?? '';
        // const url = 'https://m.efshop.com.tw/showEmptyCart';
        // const url =
        //     'https://m.efshop.com.tw/CartFinish/success/7278195?payment_name=%E4%BF%A1%E7%94%A8%E5%8D%A1%E4%BB%98%E6%AC%BE&source=android';
        // const url =
        //     'https://m.efshop.com.tw/CartFinish/fail/7278177?payment_name=%E4%BF%A1%E7%94%A8%E5%8D%A1%E4%BB%98%E6%AC%BE&source=android';
        talker.info('onUrlChange: $url');
        final uri = Uri.parse(url);
        // promotion
        if (uri.pathSegments.contains(Keys.promotion)) {
          // hack: back to cart page
          onBackPressed();
          final promotionId = uri.pathSegments.last;
          Get.toNamed(Routes.PROMOTION, parameters: {
            Keys.id: promotionId,
            Keys.action: Keys.promotion,
          });
          return;
        }
        // 空購物車
        // if (uri.pathSegments.contains(Keys.showEmptyCart)) {
        //   _empty();
        //   return;
        // }
        // 訂單結果
        // if (uri.pathSegments.contains(Keys.cartFinish)) {
        //   orderId = uri.pathSegments.last;
        //   if (uri.pathSegments.contains(Keys.success)) {
        //     _title.value = '訂購成功';
        //     this.change(Keys.success, status: RxStatus.success());
        //     return;
        //   } else if (uri.pathSegments.contains(Keys.fail)) {
        //     _title.value = '付款失敗';
        //     this.change(Keys.fail, status: RxStatus.success());
        //     return;
        //   }
        // }
        // line pay
        // https://web-pay.line.me/web/payment/wait?transactionReserveId=TWhPTDg1Q1pXSnh3L3pBcG9QTTFpdmgxZGhBdTV1Ync0cmkrVm1aTmFrZTJQOG90Y0ozVk5nSFk2MEtxRXdDSA
        // if (uri.authority == Constants.linePayAuthority) {
        //   // https: //web-pay.line.me/web/payment/waitPreLogin?transactionReserveId=TWhPTDg1Q1pXSnh3L3pBcG9QTTFpdmgxZGhBdTV1Ync0cmkrVm1aTmFrZTJQOG90Y0ozVk5nSFk2MEtxRXdDSA
        //   if (uri.pathSegments.contains('waitPreLogin')) {
        //     if (uri.queryParameters.containsKey(Keys.transactionReserveId)) {
        //       final tId = uri.queryParameters[Keys.transactionReserveId];
        //       // 'https://line.me/R/pay/payment/$tId';
        //       final lineUri = Uri.https(Constants.lineMeAuthority, '/R/pay/payment/$tId');
        //       launchUrl(lineUri, mode: LaunchMode.externalApplication);
        //     }
        //   }
        // }
        // https://m.efshop.com.tw/cart_payment
        if (uri.pathSegments.contains(Keys.cartPayment)) {
          // add_payment_info. but nothing could be got.
          _logAddPaymentInfo();
        }
        // https://m.efshop.com.tw/cart_checkout
        if (uri.pathSegments.contains(Keys.cartCheckout)) {
          _logBeginCheckout();
        }
      },
    ));
  }

  ///
  /// GA: log add payment info
  ///
  Future<void> _logAddPaymentInfo() async {
    try {
      await FirebaseAnalytics.instance.logAddPaymentInfo(
        // String? coupon,
        currency: 'TWD',
        // String? paymentType,
        value: cartItems.cartAmount.toDouble(),
        items: cartItems.items
            ?.map((e) => e.toAnalyticsEventItem())
            .toList(growable: false),
        // Map<String, Object?>? parameters,
        // AnalyticsCallOptions? callOptions,
      );
    } catch (e) {
      talker.error(e);
    }
  }

  ///
  /// GA: log begin checkout
  ///
  Future<void> _logBeginCheckout() async {
    try {
      await FirebaseAnalytics.instance.logBeginCheckout(
        value: cartItems.cartAmount.toDouble(),
        currency: 'TWD',
        items: cartItems.items
            ?.map((e) => e.toAnalyticsEventItem())
            .toList(growable: false),
      );
    } catch (e) {
      talker.error(e);
    }
  }

  ///
  /// 檢查/瀏覽購物車
  ///
  Future<void> cartViewed() async {
    try {
      final res = await wabowProvider.getCartItems();
      // appier: cartViewed
      await _appierCartViewed(res);
      // GA: log view cart
      await _logViewCart(res);
    } catch (e) {
      talker.error(e);
    }
  }

  ///
  /// appier: cartViewed
  ///
  Future<void> _appierCartViewed(CartItemsRes res) async {
    try {
      await appierProvider.cartViewed(AppierCartViewedRes(
        cartAmount: res.cartAmount,
        numberOfProducts: res.numberOfProducts,
      ));
    } catch (e) {
      talker.error(e);
    }
  }

  ///
  /// GA: log view cart
  ///
  Future<void> _logViewCart(CartItemsRes res) async {
    try {
      await FirebaseAnalytics.instance.logViewCart(
        currency: 'TWD',
        value: res.cartAmount.toDouble(),
        items: res.items
            ?.map((e) => e.toAnalyticsEventItem())
            .toList(growable: false),
      );
    } catch (e) {
      talker.error(e);
    }
  }
}
