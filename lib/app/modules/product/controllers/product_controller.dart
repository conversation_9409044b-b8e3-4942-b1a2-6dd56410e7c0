import 'dart:async';
import 'dart:math';

import 'package:efshop/app/models/appier_product_viewed_req.dart';
import 'package:efshop/app/models/configs_res.dart';
import 'package:efshop/app/models/members_my_favorite_post_req.dart';
import 'package:efshop/app/models/product_detail.dart';
import 'package:efshop/app/models/product_series_res.dart';
import 'package:efshop/app/models/products_collocation_res.dart';
import 'package:efshop/app/models/products_id_comments_res.dart';
import 'package:efshop/app/models/thumbnail.dart';
import 'package:efshop/app/providers/appier_provider.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/error_type.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/gestures.dart';
import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:talker_flutter/talker_flutter.dart';
import 'package:video_player/video_player.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

enum AnimationTarget {
  favorite,
  cart,
}

enum HorizontalPosition {
  left,
  right,
}

extension AnimationTargetX on AnimationTarget {
  HorizontalPosition get position {
    switch (this) {
      case AnimationTarget.favorite:
        return HorizontalPosition.right;
      case AnimationTarget.cart:
        return HorizontalPosition.left;
    }
  }

  double? get endLeft {
    switch (this) {
      case AnimationTarget.favorite:
        return null;
      case AnimationTarget.cart:
        return 130.0;
    }
  }

  double? get endRight {
    switch (this) {
      case AnimationTarget.favorite:
        return 0.0;
      case AnimationTarget.cart:
        return null;
    }
  }

  double get endY {
    switch (this) {
      case AnimationTarget.favorite:
        return 120.0;
      case AnimationTarget.cart:
        return 0.0;
    }
  }

  double get endX {
    switch (this) {
      case AnimationTarget.favorite:
        return 60.0;
      case AnimationTarget.cart:
        return 130.0;
    }
  }

  String get message {
    switch (this) {
      case AnimationTarget.favorite:
        return '收藏成功';
      case AnimationTarget.cart:
        return '加入購物車成功';
    }
  }
}

class ProductController extends GetxController
    with StateMixin<String>, ScrollMixin {
  final WabowProvider wabowProvider;
  Talker get talker => wabowProvider.talker;
  PrefProvider get prefProvider => wabowProvider.prefProvider;
  BoxProvider get boxProvider => wabowProvider.boxProvider;
  AppierProvider get appierProvider => wabowProvider.appierProvider;
  final _disposable = Completer();
  final _id = ''.obs;
  String get id => _id.value;
  set id(String value) => _id.value = value;
  final _data = ProductDetail().obs;
  ProductDetail get data => _data.value;
  final favorite = false.reactive;
  final _isFavorite = false.obs;
  bool get isFavorite => _isFavorite.value;
  final comments = <ProductsCommentsRes>[].obs;
  ProductsCommentsRes get comment => comments.first;
  // 圖片的 webview controller
  late final WebViewController webViewController;
  // 圖片的 webview 高度
  final _scrollHeight = 1.0.obs;
  double get scrollHeight => _scrollHeight.value;
  // banner 列表
  final serials = <ProductSeriesRes>[].obs;
  final _bannerIndex = 0.obs;
  int get bannerIndex => _bannerIndex.value;
  set bannerIndex(int value) => _bannerIndex.value = value;
  int get colorIndex => bannerIndex.clamp(0, max(colors.length - 1, 0));
  // picked serial (從 Color Picker 選擇的)
  final _pickedSerialId = RxNum(0);
  num get pickedSerialId => _pickedSerialId.value;
  // 選擇的動畫圖片
  ProductSeriesRes get animatedSerial {
    // colors 只有第一個顏色，不包含尺寸
    // serials 數量為全部 = 顏色 x 尺寸
    return serials.firstWhere((element) => element.id == pickedSerialId,
        orElse: () {
      return currentSerial;
    });
  }

  String get currentSerialColor {
    try {
      return currentSerial.color ?? '';
    } catch (e) {
      talker.error(e.toString());
      return '';
    }
  }

  ProductSeriesRes get currentSerial {
    if (colorIndex >= 0 && colorIndex < colors.length) {
      return colors.elementAt(colorIndex);
    }
    return ProductSeriesRes();
  }

  // 內容高度
  final _expandedHeight = 1.0.obs;
  double get expandedHeight => _expandedHeight.value;
  set expandedHeight(double value) => _expandedHeight.value = value;
  // 內容寬度
  final _expandedWidth = 1.0.obs;
  double get expandedWidth => _expandedWidth.value;
  set expandedWidth(double value) => _expandedWidth.value = value;
  double get aspectRatio => expandedWidth / expandedHeight;

  // 顯示移頂按鈕
  final _showFab = false.obs;
  bool get showFab => _showFab.value;
  set showFab(bool value) {
    if (_showFab.value != value) {
      _showFab.value = value;
    }
  }

  // 顯示搭配商品旗標
  final _showCollocation = false.obs;
  bool get showCollocation => _showCollocation.value;
  set showCollocation(bool value) {
    if (_showCollocation.value != value) {
      _showCollocation.value = value;
    }
  }

  // 顯示/隱藏搭配商品
  void toggleCollocation() {
    showCollocation = !showCollocation;
  }

  // distinct with color
  List<ProductSeriesRes> get colors {
    if (_colors.isEmpty) {
      _colors.assignAll(_buildColors());
    }
    return _colors;
  }

  // colors + videos
  Iterable<ProductSeriesRes> get banners sync* {
    yield* colors;
    yield* _videos;
  }

  final _colors = <ProductSeriesRes>[].obs;
  final _videos = <ProductSeriesRes>[].obs;
  // video controller
  final _videoControllers = <VideoPlayerController>[].obs;
  VideoPlayerController getVideoController(ProductSeriesRes data) {
    return _videoControllers.firstWhere(
        (element) => element.dataSource == data.mainImage, orElse: () {
      final uri = Uri.parse(data.mainImage ?? '');
      return VideoPlayerController.networkUrl(uri);
    });
  }

  List<ProductSeriesRes> _buildColors() {
    final ls =
        serials.where((element) => '${element.id}' == '${data.id}').toList();
    return serials.fold<List<ProductSeriesRes>>(<ProductSeriesRes>[],
        (previousValue, element) {
      if (previousValue.every((e) => e.mainImage != element.mainImage)) {
        if (ls.isNotEmpty && element.mainImage == ls.first.mainImage) {
          if (element.id == ls.first.id) {
            _bannerIndex.value = previousValue.length;
            previousValue.add(element);
          }
        } else {
          previousValue.add(element);
        }
      }
      return previousValue;
    });
  }

  // 商品描述的 webview 高度
  final descriptionWebViewController = WebViewController();
  final _descriptionHeight = 1.0.obs;
  double get descriptionHeight => _descriptionHeight.value;
  // 從 html 取出的圖片
  final thumbs = <Thumbnail>[].obs;
  // 是否啟用 webview 手勢
  final _enableWebViewGesture = false.obs;
  bool get enableWebViewGesture => _enableWebViewGesture.value;
  set enableWebViewGesture(bool value) {
    if (_enableWebViewGesture.value != value) {
      _enableWebViewGesture.value = value;
    }
  }

  bool get cover => !enableWebViewGesture;

  String get productNameWithColor {
    return '${data.name} - $currentSerialColor';
  }

  // 運費
  Iterable<ShippingEvent> get shippingEvents sync* {
    try {
      final ls = prefProvider.configs.shippingEvents ?? [];
      for (var element in ls) {
        if (element.isAppEnable) {
          yield element;
        }
      }
      // return ls.where((element) => element.enable ?? false);
    } catch (e) {
      talker.error(e.toString());
    }
  }

  final _animation = false.obs;
  bool get animation => _animation.value;
  set animation(bool value) {
    if (_animation.value != value) {
      _animation.value = value;
      if (animation == false) {
        Get.showToast(target.message);
        // 動畫播放完成後重置
        _pickedSerialId.value = 0;
      }
    }
  }

  final _target = AnimationTarget.cart.obs;
  AnimationTarget get target => _target.value;

  void playAnimation(AnimationTarget target, [num serialId = 0]) {
    _target.value = target;
    animation = true;
    _pickedSerialId.value = serialId;
  }

  ProductController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _initWebviewController();
    _id.stream
        .asyncMap((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
    if (Get.parameters.containsKey(Keys.id)) {
      _id.value = Get.parameters[Keys.id] ?? '';
      talker.debug('[ProductController] id: $id');
    }
  }

  void _initWebviewController() {
    late final PlatformWebViewControllerCreationParams params;
    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }
    webViewController = WebViewController.fromPlatformCreationParams(params);
    if (webViewController.platform is AndroidWebViewController) {
      // AndroidWebViewController.enableDebugging(true);
      (webViewController.platform as AndroidWebViewController)
          .setMediaPlaybackRequiresUserGesture(false);
    }
    webViewController.setJavaScriptMode(JavaScriptMode.unrestricted);
    webViewController.setNavigationDelegate(NavigationDelegate(
      onNavigationRequest: (request) {
        final url = request.url;
        talker.info('[ProductController] onNavigationRequest: $url');
        final uri = Uri.parse(url);
        if ('about:blank' == url) {
          return NavigationDecision.navigate;
        }
        // onNavigationRequest: https://www.youtube.com/embed/Jrm_kRabTi8?rel=0&showinfo=0
        if (uri.authority == 'www.youtube.com') {
          return NavigationDecision.navigate;
        }
        // if (uri.authority == 'm.youtube.com') {
        //   return NavigationDecision.navigate;
        // }
        // onNavigationRequest: https://www.efshop.com.tw/product/98943
        if (Get.canLaunchUrl(uri)) {
          Get.launchUrl(uri);
        }
        // canLaunchUrl(uri).then((value) {
        //   if (value) {
        //     launchUrl(uri);
        //   }
        // });
        return NavigationDecision.prevent;
      },
      onUrlChange: (change) {
        final url = change.url;
        talker.info('[ProductController] onUrlChange: $url');
      },
      onPageFinished: (url) async {
        talker.info('onPageFinished: $url');
        final result = await webViewController.runJavaScriptReturningResult(
            'document.documentElement.scrollHeight;');
        talker.info('onPageFinished: $result');
        // 将结果解析为双精度数值
        final height = (result as num).toDouble();
        if (GetPlatform.isIOS) {
          _scrollHeight.value = height;
        } else {
          // Android
          _scrollHeight.value = min(expandedHeight, height);
          webViewController.runJavaScript(
            '''
            window.addEventListener('scroll', function() {
              progress = (this.scrollY / ( document.body.scrollHeight - window.innerHeight ) ) * 100;
              window.ARTICLE_SCROLL_CHANNEL.postMessage(progress);
            });
            ''',
          );
        }
      },
    ));
    webViewController.addJavaScriptChannel(
      'ARTICLE_SCROLL_CHANNEL',
      onMessageReceived: (progress) {
        // talker.info('progress: ${progress.message}');
        if (int.tryParse(progress.message) == 0) {
          _enableWebViewGesture.value = false;
        }
        // setState(() {
        //   position = double.parse(progress.message) / 100;
        // });
      },
    );

    descriptionWebViewController.setJavaScriptMode(JavaScriptMode.unrestricted);
    descriptionWebViewController.setNavigationDelegate(NavigationDelegate(
      onPageFinished: (url) async {
        talker.info('onPageFinished: $url');
        final result =
            await descriptionWebViewController.runJavaScriptReturningResult(
                'document.documentElement.scrollHeight;');
        talker.info('onPageFinished: $result');
        const offset = -60.0;
        // 将结果解析为双精度数值
        _descriptionHeight.value = max(60, (result as num).toDouble() + offset);
      },
    ));
  }

  ///
  /// 手指上滑
  ///
  void onScrollUp() {
    try {
      final position = scroll.position;
      // talker.info('onScrollUp: $position');
      // offset 超過 70 則顯示 fab
      _showFab.value = position.pixels > 70;
    } catch (error, stackTrace) {
      FirebaseCrashlytics.instance.recordError(
        error,
        stackTrace,
      );
    }
  }

  ///
  /// 手指下滑
  ///
  void onScrollDown() {
    try {
      final position = scroll.position;
      // talker.info('onScrollUp: $position');
      // offset 低於 70 則隱藏 fab
      _showFab.value = position.pixels > 70;
    } catch (error, stackTrace) {
      FirebaseCrashlytics.instance.recordError(
        error,
        stackTrace,
      );
    }
  }

  void onVerticalDragUpdate(DragUpdateDetails details) {
    // talker.info('onVerticalDragUpdate');
    final dy = details.delta.dy;
    // talker.info('dy: $dy');
    final scrollDown = dy < 0;
    // final offset = scroll.offset;
    // talker.info('offset: $offset');
    final position = scroll.position;
    // talker.info('position: $position');
    final pixels = position.pixels;
    // talker.info('pixels: $pixels');
    // final maxScrollExtent = position.maxScrollExtent;
    // talker.info('maxScrollExtent: $maxScrollExtent');
    // final extentTotal = position.extentTotal;
    // talker.info('extentTotal: $extentTotal');
    if (scrollDown) {
      final reachBottom = scroll.offset >= position.maxScrollExtent;
      if (reachBottom) {
        // scroll webview
        final newOffset = -dy * Get.pixelRatio;
        webViewController.scrollBy(0, newOffset.toInt());
      } else {
        scroll.jumpTo(pixels - dy);
      }
    } else {
      // scroll up
      webViewController.getScrollPosition().then((value) {
        // talker.info('getScrollPosition: $value');
        final reachWebViewTop = value.dy <= 0;
        if (reachWebViewTop) {
          scroll.jumpTo(pixels - dy);
        } else {
          final newOffset = -dy * Get.pixelRatio;
          webViewController.scrollBy(0, newOffset.toInt());
        }
      });
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  ///
  /// 取得搭配商品
  ///
  Iterable<ProductsCollocationRes> get productsCollocation =>
      _getProductsCollocation(colorIndex);

  Iterable<ProductsCollocationRes> _getProductsCollocation(int index) {
    if (index >= 0 && index < colors.length) {
      final color = colors.elementAt(index);
      final key = '${color.id ?? 0}';
      final box = boxProvider.getGsBox(Boxes.productCollocation.name);
      if (box.hasData(key)) {
        final ls = List.from(box.read(key));
        return ls.map((e) => ProductsCollocationRes.fromJson(e));
      }
    }
    return <ProductsCollocationRes>[];
  }

  ///
  /// 獲取搭配商品
  ///
  Future<void> _fetchProductsCollocation() async {
    final box = boxProvider.getGsBox(Boxes.productCollocation.name);
    for (var color in colors) {
      final key = '${color.id ?? 0}';
      final it = await wabowProvider.getProductsCollocation(key);
      final ls = it.map((e) => e.toJson()).toList(growable: false);
      box.write(key, ls);
    }
  }

  bool get containsCollocation => _containsCollocation(colorIndex);

  bool _containsCollocation(int index) {
    if (index >= 0 && index < colors.length) {
      final color = colors.elementAt(index);
      final key = '${color.id ?? 0}';
      final box = boxProvider.getGsBox(Boxes.productCollocation.name);
      if (box.hasData(key)) {
        final ls = List.from(box.read(key));
        return ls.isNotEmpty;
      }
    }
    return false;
  }

  Future<void> pauseVideos() async {
    for (var element in _videoControllers) {
      try {
        await element.pause();
      } catch (e) {
        talker.error(e.toString());
      }
    }
  }

  Future<void> onRefresh() async {
    change('', status: RxStatus.loading());
    // 取得 series
    try {
      final it = await wabowProvider.getProductsGroup(id);
      serials.assignAll(it);
    } catch (e) {
      talker.error(e.toString());
    }
    // 取得 video
    try {
      final it = await wabowProvider.getProductsGroupImages(id);
      final controllers = it
          .map((e) => Uri.parse(e))
          .where((e) => e.pathSegments.last.endsWith('.mp4'))
          .map((e) => VideoPlayerController.networkUrl(e));
      _videoControllers.assignAll(controllers);
      _videos.assignAll(it.map((e) => ProductSeriesRes(mainImage: e)));
      final it2 = _videos
          .where((e) => e.isVideo)
          .map((e) => Uri.parse(e.mainImage ?? ''))
          .map((e) => VideoPlayerController.networkUrl(e));
      _videoControllers.assignAll(it2);
      for (var element in _videoControllers) {
        await element.initialize();
        await element.setLooping(true);
        // await element.setVolume(0.0);
        // await element.play();
      }
    } catch (e) {
      talker.error(e.toString());
    }
    // 取得商品詳情
    try {
      _data.value = await wabowProvider.getProducts(id);
      // final it = data.extractImageUrls.map(
      //   (e) => Thumbnail(
      //     src: e,
      //     width: 0,
      //     height: 1,
      //   ),
      // );
      // thumbs.assignAll(it);
      await descriptionWebViewController.loadHtmlString(data.descriptionHtml);
      await 200.milliseconds.delay();
      // await webViewController.loadHtmlString(data.descriptionImageHtml);
      change('', status: RxStatus.success());
      200.milliseconds.delay(() {
        // descriptionWebViewController.loadHtmlString(data.descriptionHtml);
        webViewController.loadHtmlString(data.descriptionImageHtml);
      });
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
    // appier event
    await _appierProductViewed();
    // GA: log view item
    await _logViewItem();
    // 取得收藏
    try {
      if (prefProvider.isNotLogin) {
        throw ErrorType.unauthorized;
      }
      _isFavorite.value =
          await wabowProvider.isMembersMyFavorite(data.number ?? '');
    } catch (e) {
      // 未登入時沒有 token 會出現 401 錯誤
      // 從本地取得收藏
      _isFavorite.value = false;
      final box = boxProvider.getGsBox(Boxes.favorite.name);
      for (var element in serials) {
        if (box.hasData(element.number ?? '')) {
          _isFavorite.value = true;
          break;
        }
      }
    }
    // 取得 comments
    try {
      final it = await wabowProvider.getProductsComments(id);
      comments.assignAll(it);
    } catch (e) {
      talker.error(e.toString());
    }
    // 取得搭配商品
    try {
      await _fetchProductsCollocation();
      // 更新搭配商品按鈕顯示
      _bannerIndex.refresh();
    } catch (e) {
      talker.error(e.toString());
    }
    // 取得 cart quantity
    wabowProvider.fetchCartQuantity();
  }

  ///
  /// appier event: product viewed
  ///
  Future<void> _appierProductViewed() async {
    try {
      await appierProvider.productViewed(AppierProductViewedReq(
        categoryName: data.topCategoryName,
        typeName: data.categoryName,
        productId: data.id,
        productName: data.name,
        productImageUrl: data.thumbnail?.src,
        productUrl: data.shareUrl,
        productPrice: data.finalPrice,
      ));
    } catch (e) {
      talker.error(e.toString());
    }
  }

  ///
  /// GA: load view item
  ///
  Future<void> _logViewItem() async {
    try {
      await FirebaseAnalytics.instance.logViewItem(
        currency: 'TWD',
        value: double.tryParse(data.price ?? ''),
        items: [data.toAnalyticsEventItem()],
        // parameters:
        // callOptions:
      );
    } catch (e) {
      talker.error(e);
    }
  }

  Future<void> _removeFromFavorite() async {
    final box = boxProvider.getGsBox(Boxes.favorite.name);
    box.remove(data.number ?? '');
    // sync to server
    if (prefProvider.isLogin) {
      try {
        await wabowProvider.deleteMembersMyFavoriteWithId(data.number ?? '');
      } catch (e) {
        talker.error(e.toString());
      }
    }
    Get.showToast('取消收藏');
  }

  Future<void> _addToFavorite() async {
    final box = boxProvider.getGsBox(Boxes.favorite.name);
    box.write(data.number ?? '', data.color ?? '');
    // sync to server
    if (prefProvider.isLogin) {
      try {
        await wabowProvider.postMembersMyFavorite(MembersMyFavoritePostReq(
          number: data.number,
          color: data.color,
        ));
      } catch (e) {
        talker.error(e.toString());
        box.remove(data.number!);
      }
    }
    // GA: log add to wishlist
    await _logAddToWishlist();
    // Get.showToast('收藏成功');
    playAnimation(AnimationTarget.favorite);
  }

  Future<void> toggleFavorite() async {
    favorite.change(false, status: RxStatus.loading());
    final future = isFavorite ? _removeFromFavorite : _addToFavorite;
    await future();
    _isFavorite.value = !_isFavorite.value;
    favorite.change(!isFavorite, status: RxStatus.success());
  }

  ///
  /// GA: log add to wishlist
  ///
  Future<void> _logAddToWishlist() async {
    try {
      await FirebaseAnalytics.instance.logAddToWishlist(
        items: [data.toAnalyticsEventItem()],
        value: double.tryParse(data.price ?? ''),
        currency: 'TWD',
        // parameters:
        // callOptions:
      );
    } catch (e) {
      talker.error(e);
    }
  }

  @override
  void onClose() {
    _disposable.complete();
    // for (var thumb in thumbs) {
    //   CachedNetworkImage.evictFromCache(thumb.src ?? '');
    // }
    // for (var image in images) {
    //   CachedNetworkImage.evictFromCache(image.mainImage ?? '');
    // }
    for (var element in _videoControllers) {
      element.pause();
      element.dispose();
    }
    super.onClose();
  }

  @override
  Future<void> onEndScroll() async {}

  @override
  Future<void> onTopScroll() async {}

  // Future<MessageRes> postCart() {
  //   return wabowProvider.postCart('${data.id}', 1);
  // }

  void jumpToTop() {
    scroll.jumpTo(0);
    webViewController.scrollTo(0, 0);
  }

  void onEndScrollWithOverscroll(num overscroll) {
    _enableWebViewGesture.value = true;
    webViewController.scrollBy(0, overscroll.toInt());
  }
}
