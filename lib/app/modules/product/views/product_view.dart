import 'package:carousel_slider/carousel_slider.dart';
import 'package:dots_indicator/dots_indicator.dart';
import 'package:efshop/app/components/action_button.dart';
import 'package:efshop/app/components/background.dart';
import 'package:efshop/app/components/ef_share.dart';
import 'package:efshop/app/components/thumbnail_image.dart';
import 'package:efshop/app/components/color_item.dart';
import 'package:efshop/app/components/ef_placeholder.dart';
import 'package:efshop/app/components/price.dart';
import 'package:efshop/app/models/error_res.dart';
import 'package:efshop/app/models/thumbnail.dart';
import 'package:efshop/app/modules/color_picker/views/color_picker_view.dart';
import 'package:efshop/app/modules/ef_web/views/ef_web_view.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';
import 'package:video_player/video_player.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../components/ef_icon_button.dart';
import '../../category/views/category_view.dart';
import '../../comment/views/comment_view.dart';
import '../controllers/product_controller.dart';
import 'collocation_view.dart';

class ProductView extends GetView<ProductController> {
  final String? tag;

  ProductView({super.key}) : tag = '${DateTime.now().hashCode}';

  @override
  Widget build(BuildContext context) {
    Get.lazyPut(
      () => ProductController(
        wabowProvider: Get.find(),
      ),
      fenix: true,
      tag: tag,
    );
    return ColoredBox(
      color: Colors.white,
      child: SafeArea(
        child: controller.obx(
          (state) {
            return Background(
              background: Column(
                children: [
                  _subRouter().expanded(),
                  // 底部按鈕
                  _bottomButtons(),
                ],
              ),
              // 加入購物車動畫
              child: Obx(() => _animationField()),
            );
          },
          onLoading: _loading(),
          onError: _error,
        ),
      ),
    );
  }

  // 加入購物車動畫
  Widget _animationField() {
    const circleRadius = 30.0;
    final startY = Get.height * 0.5;
    final endY = controller.target.endY;
    final startX = Get.width * 0.5 - circleRadius;
    final endLeft = controller.target.endLeft;
    final endRight = controller.target.endRight;
    return AnimatedPositioned(
      bottom: controller.animation ? endY : startY,
      left: controller.animation ? endLeft : startX,
      right: controller.animation ? endRight : startX,
      curve: Curves.easeInBack,
      duration: 0.6.seconds,
      child: Visibility(
        visible: controller.animation,
        child: Material(
          color: Colors.transparent,
          child: CircleAvatar(
            radius: circleRadius,
            backgroundColor: Colors.red,
            child: ClipOval(
              child: ThumbnailImage(Thumbnail(
                src: controller.animatedSerial.mainImage,
                width: 1,
                height: 1,
              )),
            ),
          ),
        ),
      ),
      onEnd: () {
        controller.talker.info('onEnd');
        controller.animation = false;
      },
    );
  }

  // 子路由
  Widget _subRouter() {
    return Navigator(
      key: Get.nestedKey(int.tryParse(tag ?? '') ?? 0),
      onGenerateRoute: (settings) {
        controller.talker.info('[ProductView] settings: $settings');
        final uri = Uri.parse(settings.name ?? '');
        Get.parameters = uri.queryParameters;
        // 商品評價
        if (Routes.COMMENT == uri.path) {
          return GetPageRoute(
            settings: settings,
            parameter: uri.queryParameters,
            page: () => CommentView(),
          );
        }
        // 尺寸說明
        if (Routes.EF_WEB == uri.path) {
          return GetPageRoute(
            settings: settings,
            parameter: uri.queryParameters,
            page: () => EfWebView(),
          );
        }
        if (Routes.PROMOTION == uri.path) {
          return GetPageRoute(
            settings: settings,
            parameter: uri.queryParameters,
            page: () => CategoryView(),
          );
        }
        // root
        // settings: RouteSettings("/", null)
        return GetPageRoute(
          settings: settings,
          page: () => _root(),
        );
      },
    );
  }

  Widget _root() {
    return Scaffold(
      backgroundColor: Colors.white,
      // extendBody: true,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        shadowColor: Colors.transparent,
        leading: EfIconButton(
          size: 38.dw,
          onPressed: _back,
          icon: SvgPicture.asset('assets/images/icon_back.svg'),
          backgroundColor: EfColors.buttonBackground,
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        // title: const Text('產品'),
        centerTitle: true,
        actions: _actions().toList(growable: false),
      ),
      body: _body(),
    );
  }

  Widget _error(String? error) {
    // Future(() => Get.showAlert(error ?? '')).then((value) => Get.back());
    Future.microtask(() {
      Get.offNamed(Routes.NOT_FOUND);
    });
    return const SizedBox();
    // return Scaffold(
    //   backgroundColor: Colors.white,
    //   appBar: AppBar(
    //     shadowColor: Colors.transparent,
    //     backgroundColor: Colors.transparent,
    //     elevation: 0,
    //   ),
    //   body: ErrorButton(
    //     error,
    //     onTap: controller.onRefresh,
    //   ),
    // );
  }

  Widget _loading() {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        shadowColor: Colors.transparent,
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Background(
        background: Column(
          children: [
            const Spacer(),
            SizedBox(
              width: double.infinity,
              height: 1,
              child: WebViewWidget(
                controller: controller.webViewController,
              ),
            ),
            SizedBox(
              width: double.infinity,
              height: 1,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.dw),
                child: WebViewWidget(
                  controller: controller.descriptionWebViewController,
                ),
              ),
            ),
          ],
        ),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      ),
    );
  }

  Iterable<Widget> _actions() sync* {
    yield EfIconButton(
      onPressed: () {
        // 分享APP
        // Share.share(controller.data.shareUrl ?? '');
        EfShare(
          controller.data,
          flutterShareMe: Get.find(),
          talker: controller.talker,
        ).sheet();
      },
      icon: SvgPicture.asset('assets/images/icon_share.svg'),
      size: 38.dw,
      backgroundColor: EfColors.buttonBackground,
    );
  }

  Widget _androidImagesWebView() {
    return AspectRatio(
      aspectRatio: controller.aspectRatio,
      child: Background(
        background: WebViewWidget(
          controller: controller.webViewController,
          gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
            Factory(() => EagerGestureRecognizer()),
            // Factory<VerticalDragGestureRecognizer>(
            //   () => VerticalDragGestureRecognizer(),
            // ),
          },
        ),
        child: Obx(() {
          return Visibility(
            visible: controller.cover,
            child: const ColoredBox(
              color: Colors.transparent,
              child: SizedBox.expand(),
            ),
          );
        }),
      ),
    );
  }

  Widget _iosImagesWebView() {
    return SizedBox(
      width: double.infinity,
      height: controller.scrollHeight,
      child: WebViewWidget(
        controller: controller.webViewController,
      ),
    );
  }

  ///
  /// WebView
  ///
  Widget _imagesWebView() {
    return Obx(() {
      if (controller.scrollHeight == 1) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.all(8.0),
            child: CircularProgressIndicator(),
          ),
        );
      }
      return Visibility(
        visible: GetPlatform.isIOS,
        replacement: _androidImagesWebView(),
        child: _iosImagesWebView(),
      );
    });
  }

  ///
  /// 底部按鈕
  ///
  Widget _bottomButtons() {
    Iterable<Widget> children() sync* {
      yield SizedBox(
        width: 64.dw,
        child: TextButton(
          onPressed: _backToHome,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                'assets/images/icon_home.svg',
                width: 24.dw,
                height: 22.dh,
              ),
              const Text(
                '首頁',
                style: TextStyle(
                  fontSize: 12,
                  color: EfColors.gray,
                ),
              ),
            ],
          ),
        ),
      );
      yield const VerticalDivider(
        color: EfColors.grayEF,
        thickness: 1,
        width: 1,
      );
      yield Container(
        decoration: const BoxDecoration(
          color: EfColors.grayEE,
        ),
        height: double.infinity,
        width: 64.dw,
        child: TextButton(
          onPressed: _showCustomerService,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                'assets/images/customer_service_gray.svg',
                width: 28.dw,
                height: 22.dh,
              ),
              const Text(
                '客服',
                style: TextStyle(
                  fontSize: 12,
                  color: EfColors.gray,
                ),
              ),
            ],
          ),
        ),
      );
      yield const VerticalDivider(
        color: EfColors.grayEF,
        thickness: 1,
        width: 1,
      );
      yield SizedBox(
        width: 64.dw,
        child: TextButton(
          onPressed: _backToCart,
          child: Center(
            child: Obx(() {
              final badgeCount = controller.prefProvider.cartQuantity;
              return Badge.count(
                isLabelVisible: badgeCount > 0,
                count: badgeCount,
                offset: const Offset(6, -4),
                child: SvgPicture.asset(
                  'assets/images/icon_cart.svg',
                  width: 34.dw,
                  height: 32.dh,
                ),
              );
            }),
          ),
        ),
      );
      yield const VerticalDivider(
        color: EfColors.grayEF,
        thickness: 1,
        width: 1,
      );
      yield Expanded(
        child: TextButton(
          onPressed: _showColorPicker,
          style: TextButton.styleFrom(
            backgroundColor: const Color(0xffffab27),
          ),
          child: const Center(
            child: Text(
              '加入購物車',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
              softWrap: false,
            ),
          ),
        ),
      );
      yield const SizedBox(
        width: 1,
        height: double.infinity,
      );
      yield Expanded(
        child: TextButton(
          onPressed: _backToCart,
          style: TextButton.styleFrom(
            backgroundColor: EfColors.primary,
          ),
          child: const Center(
            child: Text(
              '結 帳',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
              softWrap: false,
            ),
          ),
        ),
      );
    }

    return SizedBox(
      height: 55.dh,
      child: ColoredBox(
        color: const Color(0xfff9faf9),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: children().toList(growable: false),
        ),
      ),
    );
  }

  Future<void> _toggleFavorite() async {
    try {
      await controller.toggleFavorite();
    } on ErrorRes catch (e, stackTrace) {
      await FirebaseCrashlytics.instance.log(e.error ?? '');
      await FirebaseCrashlytics.instance.recordError(
        e,
        stackTrace,
        reason: e.type,
      );
    } catch (error, stackTrace) {
      // Get.showAlert(e.toString());
      await FirebaseCrashlytics.instance.log(error.toString());
      await FirebaseCrashlytics.instance.recordError(
        error,
        stackTrace,
      );
    }
  }

  ///
  /// 回上一頁
  ///
  Future<void> _back() async {
    try {
      await controller.pauseVideos();
      Get.back();
    } catch (e) {
      controller.talker.error(e);
    }
  }

  ///
  /// 回到首頁
  ///
  Future<void> _backToHome() async {
    try {
      await controller.pauseVideos();
      await Get.offAllNamed(Routes.LOADING,
          parameters: <String, String>{Keys.id: '${IndexTab.home.index}'});
    } catch (e) {
      controller.talker.error(e);
    }
  }

  ///
  /// 回到購物車
  ///
  Future<void> _backToCart() async {
    try {
      await controller.pauseVideos();
      if (controller.prefProvider.cartQuantity > 0) {
        // 檢查是否登入
        final res = await getLoginRes();
        if (res == null || res.isExpired) {
          throw Exception('Login failed');
        }
      }
      await Get.offAllNamed(Routes.LOADING,
          parameters: <String, String>{Keys.id: '${IndexTab.cart.index}'});
    } catch (e) {
      controller.talker.error(e);
    }
  }

  ///
  /// 開啟線上客服
  ///
  Future<void> _showCustomerService() async {
    try {
      await controller.pauseVideos();
      await Get.toNamed(Routes.CHAT);
    } catch (e) {
      controller.talker.error(e);
    }
  }

  Future<void> _showColorPicker() async {
    final res = await ColorPickerView(
      id: controller.data.id,
      ids: controller.colors.map((element) => element.id),
      index: controller.colorIndex,
    ).sheet();
    if (res is num) {
      controller.playAnimation(AnimationTarget.cart, res);
    }
  }

  Widget _body() {
    Iterable<Widget> children() sync* {
      yield Background(
        background: _top(),
        child: _scrollToTopButton(),
      ).expanded();
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: children().toList(growable: false),
    );
  }

  Widget _singleChildScrollView() {
    Iterable<Widget> children() sync* {
      yield Obx(() {
        return SizedBox(
          width: double.infinity,
          height: controller.expandedHeight,
          child: _imageAndInfo(),
        );
      });
      yield* _children();
      yield _imagesWebView();
    }

    return LayoutBuilder(builder: (context, constraints) {
      controller.expandedWidth = constraints.maxWidth;
      controller.expandedHeight = constraints.maxHeight;
      return ListView(
        padding: EdgeInsets.zero,
        controller: controller.scroll,
        children: children().toList(growable: false),
      );
      // return SingleChildScrollView(
      //   controller: controller.scroll,
      //   child: Column(
      //     mainAxisSize: MainAxisSize.min,
      //     children: children().toList(growable: false),
      //   ),
      // );
    });
  }

  Widget _top() {
    return NotificationListener<ScrollNotification>(
      onNotification: (notification) {
        final metrics = notification.metrics;
        if (metrics.axis == Axis.horizontal) {
          return false;
        }
        // metrics.isAtBottom;
        // metrics.isAtTop;
        // metrics.atEdge;
        // metrics.devicePixelRatio;
        // controller.talker.info('pixels: ${metrics.pixels}');
        // if (GetPlatform.isIOS) {
        //   if (metrics.outOfRange) {
        //     controller.talker.info('outOfRange');
        //     if (metrics.isAtBottom) {
        //       final overscroll = metrics.pixels - metrics.maxScrollExtent;
        //       controller.talker.info('overscroll: $overscroll');
        //       // controller.onEndScrollWithOverscroll(overscroll / metrics.devicePixelRatio);
        //       controller.onEndScrollWithOverscroll(0);
        //       return false;
        //     }
        //   }
        // }
        // if (notification.metrics.pixels == notification.metrics.minScrollExtent) {
        // if (notification.metrics.pixels == 0) {
        //   controller.talker.info('滚动到顶部');
        //   controller.logger
        //       .i('minScrollExtent: ${notification.metrics.minScrollExtent}');
        //   // onTopScroll();
        // } else if (notification.metrics.pixels ==
        //     notification.metrics.maxScrollExtent) {
        //   controller.talker.info('滚动到底部');
        //   // onEndScroll();
        // }
        if (notification is OverscrollNotification) {
          final overscroll = notification.overscroll;
          controller.talker.info('overscroll: $overscroll');
          if (overscroll > 0) {
            // 手指上滑
            // controller.onScrollUp();
            controller.onEndScrollWithOverscroll(overscroll);
          } else if (overscroll < 0) {
            // 手指下滑
            // controller.onScrollDown();
          }
        } else if (notification is ScrollUpdateNotification) {
          final scrollDelta = notification.scrollDelta ?? 0;
          // controller.talker.info('scrollDelta: $scrollDelta');
          if (scrollDelta > 0) {
            // 手指上滑
            controller.onScrollUp();
          } else if (scrollDelta < 0) {
            // 手指下滑
            controller.onScrollDown();
          }
        }
        return true;
      },
      child: _singleChildScrollView(),
    );
  }

  Widget _scrollToTopButton() {
    return Align(
      alignment: Alignment.bottomRight,
      child: Padding(
        padding: EdgeInsets.only(
          bottom: 8.dh,
          right: 8.dw,
        ),
        child: Obx(() {
          return Visibility(
            visible: controller.showFab,
            child: EfIconButton(
              onPressed: () {
                // 回到最上方
                controller.jumpToTop();
              },
              icon: SvgPicture.asset('assets/images/icon_expand_less.svg'),
              size: 38.dw,
              backgroundColor: const Color(0xfff2f2f5),
            ),
          );
        }),
      ),
    );
  }

  ///
  /// 輪播圖
  ///
  Widget _banner() {
    Widget mixinButton() {
      return Align(
        alignment: Alignment.bottomRight,
        child: Padding(
          padding: EdgeInsets.only(
            bottom: 8.dh,
            right: 8.dw,
          ),
          child: EfIconButton(
            onPressed: () {
              // 顯示/隱藏搭配購
              controller.toggleCollocation();
              // CollocationView(controller.productsCollocation).sheet();
            },
            icon: SvgPicture.asset('assets/images/icon_clothing.svg'),
            size: 38.dw,
            backgroundColor: EfColors.buttonBackground,
          ),
        ),
      );
    }

    final it = controller.banners;
    return Background(
      alignment: Alignment.bottomCenter,
      background: CarouselSlider.builder(
        itemCount: it.length,
        itemBuilder: (context, index, realIndex) {
          controller.talker
              .debug('[ProductView] index: $index, realIndex: $realIndex');
          final data = it.elementAt(index);
          // log id
          controller.talker.debug('[ProductView] id: ${data.id}');
          if (data.isVideo) {
            final videoController = controller.getVideoController(data);
            return SizedBox(
              width: double.infinity,
              child: FittedBox(
                alignment: Alignment.center,
                fit: BoxFit.cover,
                child: SizedBox(
                  width: videoController.value.size.width,
                  height: videoController.value.size.height,
                  child: VideoPlayer(videoController),
                ),
              ),
            );
          }
          // return ThumbnailImage.url(data.mainImage);
          return ThumbnailImage(Thumbnail(
            src: data.mainImage,
            width: 1,
            height: 1,
          ));
        },
        options: CarouselOptions(
          height: double.infinity,
          // height: 245.dh,
          // viewportFraction: 1,
          autoPlay: false,
          autoPlayInterval: const Duration(seconds: 5),
          autoPlayAnimationDuration: const Duration(milliseconds: 800),
          // autoPlayCurve: Curves.fastOutSlowIn,
          // enlargeCenterPage: true,
          // onPageChanged: (index, reason) {
          //   controller.current = index;
          // },
          // aspectRatio: 375.0 / 532.0,
          // aspectRatio: controller.data.thumbnail?.aspectRatio ?? 1,
          viewportFraction: 1,
          initialPage: controller.bannerIndex,
          enableInfiniteScroll: it.length > 1,
          reverse: false,
          // autoPlay: true,
          // autoPlayInterval: Duration(seconds: 3),
          // autoPlayAnimationDuration: Duration(milliseconds: 800),
          autoPlayCurve: Curves.fastOutSlowIn,
          // enlargeCenterPage: true,
          // enlargeFactor: 0.3,
          onPageChanged: (index, reason) async {
            try {
              controller.talker.info('index: $index, reason: $reason');
              controller.bannerIndex = index;
              await controller.pauseVideos();
              final data = it.elementAt(index);
              if (data.isVideo) {
                final videoController = controller.getVideoController(data);
                await videoController.play();
              }
            } catch (e) {
              controller.talker.error(e);
            }
          },
          scrollDirection: Axis.horizontal,
        ),
      ),
      child: Background(
        alignment: Alignment.bottomCenter,
        background: it.length > 1
            ? Obx(() {
                return DotsIndicator(
                  dotsCount: it.length,
                  position: controller.bannerIndex,
                );
              })
            : const SizedBox(),
        child: Obx(() {
          return Visibility(
            visible: controller.containsCollocation,
            child: mixinButton(),
          );
        }),
      ),
    );
  }

  Widget _imageAndInfo() {
    Iterable<Widget> children() sync* {
      yield Expanded(
        child: Visibility(
          visible: controller.banners.isNotEmpty,
          replacement: const EfPlaceholder(),
          // maintainState: true,
          // maintainAnimation: true,
          // maintainSize: true,
          // maintainInteractivity: true,
          child: _banner(),
        ),
      );
      yield Container(
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
        height: 102.dh,
        child: Background(
          background: _info(),
          child: Obx(() {
            return Visibility(
              visible:
                  controller.containsCollocation && controller.showCollocation,
              child: CollocationView(
                controller.productsCollocation,
                onTap: (value) {
                  Get.toNamed(
                    Routes.PRODUCT,
                    parameters: <String, String>{
                      Keys.id: '${value.id ?? 0}',
                    },
                  );
                },
              ),
            );
          }),
        ),
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: children().toList(growable: false),
    );
  }

  Widget _separator(double height) {
    return SizedBox(
      height: height,
      child: const ColoredBox(
        color: EfColors.grayF6,
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    final data = controller.data;
    if (data.containsPromotion) {
      yield _separator(6.dh);
      yield ListTile(
        leading: const Text(
          '活動',
          style: TextStyle(
            fontSize: 15,
            color: EfColors.gray93,
          ),
          softWrap: false,
        ),
        title: Text(
          // '期間限定・一件260',
          data.promotionName ?? '',
          style: const TextStyle(
            fontSize: 15,
            color: EfColors.grayText,
          ),
          softWrap: false,
        ),
        trailing: const Icon(Icons.chevron_right),
        onTap: () {
          Get.toNamed(
            Routes.PROMOTION,
            parameters: <String, String>{
              Keys.id: '${data.promotionId ?? 0}',
              Keys.action: Keys.promotion,
            },
            // id: int.tryParse(tag ?? '') ?? 0,
          );
        },
      );
    }
    // 運費
    if (controller.shippingEvents.isNotEmpty) {
      yield _separator(6.dh);
      yield _shipping();
    }
    yield _separator(6.dh);
    yield _color();
    if (data.mobileShowComment == 1) {
      yield _separator(6.dh);
      yield* _comment();
    }
    if (data.mobileShowSizes == 1) {
      yield _separator(6.dh);
      yield* _description();
    }
    yield _separator(6.dh);
  }

  Iterable<Widget> _description() sync* {
    yield ColoredBox(
      color: Colors.white,
      child: Padding(
        padding: EdgeInsets.only(
          left: 16.dw,
          right: 16.dw,
          top: 12.dh,
        ),
        child: Obx(() {
          return SizedBox(
            width: double.infinity,
            height: controller.descriptionHeight,
            child: WebViewWidget(
              controller: controller.descriptionWebViewController,
            ),
          );
        }),
      ),
    );
    yield ListTile(
      trailing: const Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '尺寸說明',
            style: TextStyle(
              fontSize: 15,
              color: EfColors.grayText,
            ),
            softWrap: false,
          ),
          Icon(Icons.chevron_right),
        ],
      ),
      onTap: () {
        Get.toNamed(
          Routes.EF_WEB,
          parameters: <String, String>{
            Keys.url: controller.data.htmlString,
            Keys.title: '尺寸',
          },
          id: int.tryParse(tag ?? '') ?? 0,
        );
      },
    );
  }

  Iterable<Widget> _comment() sync* {
    final data = controller.data;
    yield ListTile(
      title: Text(
        '商品評價(${(data.commentCount ?? 0).decimalStyle})',
        style: const TextStyle(
          fontSize: 15,
          color: EfColors.grayText,
        ),
        softWrap: false,
      ),
      trailing: const Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '查看全部',
            style: TextStyle(
              fontSize: 15,
              color: EfColors.grayText,
            ),
            softWrap: false,
          ),
          Icon(Icons.chevron_right),
        ],
      ),
      onTap: () {
        Get.toNamed(
          Routes.COMMENT,
          parameters: <String, String>{
            Keys.id: data.id ?? '',
          },
          id: int.tryParse(tag ?? '') ?? 0,
        );
      },
    );
    yield Obx(() {
      if (controller.comments.isEmpty) {
        return const SizedBox();
      }
      return ListTile(
        title: Text(
          // '訂單後四碼：6431',
          '訂單後四碼：${controller.comment.simpleOrderNumber}',
          style: const TextStyle(
            fontSize: 14,
            color: EfColors.gray93,
          ),
          softWrap: false,
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(top: 8.0),
          child: Text(
            // '整套穿起來感受很輕盈，不會太貼身！CP值超高，適合夏天穿著！',
            controller.comment.userComment ?? '',
            style: const TextStyle(
              fontSize: 14,
              color: EfColors.grayText,
            ),
          ),
        ),
        onTap: () {
          Get.toNamed(
            Routes.COMMENT,
            parameters: <String, String>{
              Keys.id: data.id ?? '',
            },
            id: int.tryParse(tag ?? '') ?? 0,
          );
        },
      );
    });
  }

  Widget _shipping() {
    Iterable<Widget> children() sync* {
      final it = controller.shippingEvents;
      for (var i = 0; i < it.length; i++) {
        final data = it.elementAt(i);
        yield _ShippingItem(
          leadingText: data.eventPrefix ?? '',
          titleText: data.eventName ?? '',
          onTap: () async {
            try {
              final uri = Uri.parse(data.eventUrl ?? '');
              // url launcher
              if (Get.canLaunchUrl(uri)) {
                await Get.launchUrl(uri);
              }
            } catch (e) {
              Get.showAlert(e.toString());
            }
          },
        );
        if (i < it.length - 1) {
          yield const SizedBox(height: 8);
        }
      }
      // yield const _ShippingItem(
      //   leadingText: '優惠',
      //   titleText: '歡慶母親節',
      // );
      // yield const SizedBox(height: 8);
      // yield const _ShippingItem(
      //   leadingText: '超商取貨',
      //   titleText: '滿額699 免運費！',
      // );
    }

    return ListTile(
      leading: Transform.translate(
        offset: const Offset(0, 0),
        child: const Text(
          '優惠',
          style: TextStyle(
            fontSize: 15,
            color: EfColors.gray93,
          ),
          softWrap: false,
        ),
      ),
      // title: ListView.separated(
      //   shrinkWrap: true,
      //   physics: const NeverScrollableScrollPhysics(),
      //   itemBuilder: (context, index) {
      //     final data = it.elementAt(index);
      //     return _ShippingItem(
      //       leadingText: data.eventPrefix ?? '',
      //       titleText: data.eventName ?? '',
      //     );
      //   },
      //   separatorBuilder: (context, index) {
      //     return const SizedBox(height: 8);
      //   },
      //   itemCount: it.length,
      // ),
      title: Column(
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
      // trailing: const Icon(Icons.chevron_right),
      // onTap: () {},
    );
  }

  Widget _colorCount(num count) {
    return Padding(
      padding: const EdgeInsets.all(4.0),
      child: ColoredBox(
        color: EfColors.grayF6,
        child: SizedBox.square(
          dimension: 44,
          child: Center(
            child: Text(
              '$count色',
              style: const TextStyle(
                fontSize: 14,
                color: EfColors.grayText,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _color() {
    return ListTile(
      leading: const Text(
        '顏色',
        style: TextStyle(
          fontSize: 15,
          color: EfColors.gray93,
        ),
        softWrap: false,
      ),
      title: ClipRect(
        child: ConstrainedBox(
          constraints: const BoxConstraints(
            maxHeight: 104,
          ),
          child: LayoutBuilder(
            builder: (context, constraints) {
              controller.talker.info('constraints: $constraints');
              final count = constraints.biggest.width ~/ (8.0 + 48.0 + 8.0);
              final maxCount = count * 2;
              final colorCount = controller.colors.length;
              // 顏色數量是否超過限制
              final overflow = colorCount > maxCount;
              // 擷取的數量
              final fetchCount = overflow ? maxCount - 1 : colorCount;
              // 依數量擷取顏色列表
              final ls = controller.colors.sublist(0, fetchCount);
              final children = <Widget>[];
              children.addAll(ls.map((e) => ColorItem(e)));
              if (overflow) {
                children.add(_colorCount(colorCount));
              }
              return Wrap(
                // spacing: 8.dw,
                // runSpacing: 8.dh,
                children: children,
                // children: children().toList(growable: false),
                // children: controller.colors.map((element) {
                //   return ColorItem(element);
                // }).toList(growable: false),
              );
            },
          ),
        ),
      ),
      trailing: const Icon(Icons.chevron_right),
      onTap: _showColorPicker,
    );
  }

  Widget _info() {
    Iterable<Widget> children() sync* {
      final data = controller.data;
      // yield SizedBox(height: 10.dw);
      // yield Text(
      //   // '羅紋配色V領純棉T恤-紅',
      //   controller.productNameWithColor,
      //   style: const TextStyle(
      //     fontSize: 15,
      //     color: EfColors.grayText,
      //   ),
      //   maxLines: 2,
      //   overflow: TextOverflow.ellipsis,
      //   softWrap: true,
      // ).expanded();
      yield Row(
        children: [
          Flexible(
            fit: FlexFit.tight,
            child: Price(
              data.finalPrice,
              color: EfColors.primary,
              fontSize: 26,
              originalPrice: data.originalPrice,
            ),
          ),
          Text(
            '已售：${(data.soldTotal ?? 0).decimalStyle}件',
            style: const TextStyle(
              fontSize: 15,
              color: EfColors.grayText,
            ),
            softWrap: false,
          ),
          SizedBox(
            width: 32.dw,
            child: TextButton(
              onPressed: _toggleFavorite,
              child: Center(
                child: Obx(() {
                  return Visibility(
                    visible: controller.isFavorite,
                    replacement: SvgPicture.asset(
                      'assets/images/icon_favorite.svg',
                      width: 24.dw,
                      height: 22.dh,
                    ),
                    child: SvgPicture.asset(
                      'assets/images/favorite.svg',
                      width: 30.dw,
                      height: 28.dh,
                    ),
                  );
                }),
              ),
            ),
          ),
        ],
      );
      // yield SizedBox(height: 4.dw);
      yield Text.rich(
        TextSpan(
          children: [
            TextSpan(
              text: data.name ?? '',
              style: const TextStyle(
                fontSize: 15,
                color: EfColors.grayText,
              ),
            ),
            TextSpan(
              text: ' - ${controller.currentSerialColor}',
              style: const TextStyle(
                fontSize: 15,
                color: EfColors.grayTextLight,
              ),
            ),
          ],
        ),
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        softWrap: true,
      ).expanded();
      // yield SizedBox(height: 12.dw);
    }

    return ColoredBox(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ).paddingSymmetric(horizontal: 8.dw),
    );
  }
}

class _ShippingItem extends StatelessWidget {
  final String leadingText;
  final String titleText;
  final VoidCallback? onTap;

  const _ShippingItem({
    super.key,
    this.leadingText = '',
    this.titleText = '',
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: _children().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    // stadium
    yield DecoratedBox(
      decoration: const ShapeDecoration(
        shape: StadiumBorder(),
        color: EfColors.primary,
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: 12.dw,
          vertical: 4.dh,
        ),
        child: Text(
          // '優惠',
          leadingText,
          style: const TextStyle(
            fontSize: 15,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
    yield SizedBox(width: 4.dw);
    yield Expanded(
      child: Text(
        // '歡慶母親節',
        titleText,
        style: const TextStyle(
          fontSize: 15,
          color: EfColors.grayText,
        ),
        softWrap: false,
      ),
    );
  }
}
