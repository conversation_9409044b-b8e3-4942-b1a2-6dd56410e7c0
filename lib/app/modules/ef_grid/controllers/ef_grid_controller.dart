import 'dart:async';
import 'dart:math' show max;

import 'package:efshop/app/models/app.dart';
import 'package:efshop/app/models/categories_id_quantity_req.dart';
import 'package:efshop/app/models/category.dart';
import 'package:efshop/app/models/product_detail.dart';
import 'package:efshop/app/models/promotion_detail_res.dart';
import 'package:efshop/app/models/promotion_id_req.dart';
import 'package:efshop/app/models/url.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/pref_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart' show TabController, PageController;
import 'package:flutter/widgets.dart' show WidgetsBinding;
import 'package:flutter/rendering.dart';
import 'package:get/get.dart';

import 'package:stream_transform/stream_transform.dart';
import 'package:talker_flutter/talker_flutter.dart';

class EfGridController extends GetxController
    with StateMixin<String>, ScrollMixin, GetSingleTickerProviderStateMixin {
  final _disposable = Completer();
  final WabowProvider wabowProvider;
  PrefProvider get prefProvider => wabowProvider.prefProvider;
  BoxProvider get boxProvider => wabowProvider.boxProvider;
  Talker get talker => wabowProvider.talker;

  final appHeaders = <App>[].obs;
  final headers = <ProductDetail>[].obs;
  final products = <ProductDetail>[].obs;
  String get id => url.id ?? '';
  // promotion
  final _promotion = Promotion().obs;
  Promotion get promotion => _promotion.value;
  Iterable<Threshold> get thresholds => promotion.thresholds ?? [];
  // current page
  Iterable<ProductDetail> getCurrentProducts(int index) {
    if (tabs.isEmpty) {
      return products;
    }
    if (promotion.hasThreshold) {
      return products;
    }
    // final index = tabController.index;
    talker.debug('tabController.index: $index');
    final tab = tabs.elementAt(index);
    final key = tab.key;
    talker.debug('tab.key: $key');
    return products.where((product) {
      final category = product.category ?? {};
      return category.containsKey(tab.key);
    });
  }

  // panel height
  final _panelHeight = 0.0.obs;
  set panelHeight(double value) => _panelHeight.value = value;
  double get panelHeight => _panelHeight.value;

  final _page = 1.obs;
  int get page => _page.value;
  int get needLoadMore => page == 0 ? 0 : 1;
  static const _countPerPage = 500;

  final _url = Url().obs;
  Url get url => _url.value;
  set url(Url value) => _url.value = value;
  final _category = Rx<Category?>(null);
  Category? get category => _category.value;
  set category(Category? value) => _category.value = value;
  // tab controller
  final _tabController = Rx<TabController?>(null);
  TabController? get tabController {
    if (_tabController.value == null) {
      final ls = List.from(tabs, growable: false);
      final tabCount = ls.isEmpty ? 1 : ls.length;
      final index = ls.indexWhere((element) => element.key == url.category);
      url.category;
      _tabController.value = TabController(
        vsync: this,
        length: tabCount,
        initialIndex: ls.isEmpty ? 0 : index.clamp(0, ls.length - 1),
      );
    }
    return _tabController.value;
  }

  // page controller
  final _pageController = Rx<PageController?>(null);
  PageController get pageController {
    final currentIndex = tabs.isEmpty ? 0 : (tabController?.index ?? 0);
    _pageController.value ??= PageController(
      keepPage: false,
      viewportFraction: 1,
      initialPage: currentIndex,
    );
    final res = _pageController.value!;
    if (res.initialPage != currentIndex) {
      _safeJumpToPage(currentIndex);
    }
    return res;
  }

  /// 安全地跳轉到指定頁面，避免 "Bad state: No element" 錯誤
  void _safeJumpToPage(int index) {
    final controller = _pageController.value;
    if (controller == null || tabs.isEmpty) return;

    // 使用 addPostFrameCallback 確保在 widget 構建完成後執行
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        // 檢查 PageController 是否有有效的 position
        if (controller.hasClients && controller.positions.isNotEmpty) {
          final safeIndex = index.clamp(0, tabs.length - 1);
          controller.jumpToPage(safeIndex);
        } else {
          // 如果還沒有 clients，延遲重試
          Future.delayed(const Duration(milliseconds: 100), () {
            _safeJumpToPage(index);
          });
        }
      } catch (e, s) {
        talker.error('Failed to jump to page $index: $e', e, s);
      }
    });
  }

  // tabs
  final _tabs = {}.obs;
  Iterable<MapEntry> get tabs {
    final it = _tabs.entries.where((element) => element.value.isNotEmpty);
    final tabs = it.toList(growable: false);
    // 排序
    tabs.sort((a, b) {
      final x = _categories.indexWhere((element) => element.uniqueId == a.key);
      final y = _categories.indexWhere((element) => element.uniqueId == b.key);
      return x.compareTo(y);
    });
    return tabs;
  }

  // sorted categories
  final _categories = <Category>[].obs;

  num get remainCount {
    // log id
    talker.debug('id: $id');
    final itemCount = prefProvider.itemCountWithPromotionId(id);
    // 比較 promotionItems.length 和 threadholds 中的 quantity 找到未超過的最大值
    num quantity = 0;
    for (var i = 0; i < thresholds.length; i++) {
      final element = thresholds.elementAt(i);
      if (itemCount < element.numOfQuantity) {
        quantity = element.numOfQuantity;
        break;
      }
    }
    return max(0, quantity - itemCount);
  }

  EfGridController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _url.stream
        .map((event) => event.id ?? '')
        .where((event) => event.isNotEmpty)
        .distinct()
        .tap((_) => change('', status: RxStatus.loading()))
        .debounce(300.milliseconds)
        .asyncMap((event) => onRefresh(event))
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  @override
  void onReady() {
    super.onReady();
    scroll.watch().takeUntil(_disposable.future).listen((event) {
      if (scroll.position.userScrollDirection == ScrollDirection.reverse) {
        // 如果滑動距離超過 300，則 panelHeight 設為 150
        if (scroll.position.pixels > 800) {
          _panelHeight.value = 108.0;
        }
      } else if (scroll.position.userScrollDirection ==
          ScrollDirection.forward) {
        _panelHeight.value = 0.0;
      }
    });
  }

  @override
  void onClose() {
    _disposable.complete();
    _tabController.value?.dispose();
    _pageController.value?.dispose();
    super.onClose();
  }

  Future<Iterable<ProductDetail>> _fetchFromCategory(String event) async {
    talker.debug('[EfGridController] fetch category: $event');
    final res = await wabowProvider.getCategoriesAppWithIdAndQuantity(
      event,
      _countPerPage,
      CategoriesIdQuantityReq(
        targetPage: page,
        // sortWay: 'desc', // asc, desc
        // sortField: 'create_date', // sold_total, create_date, price
      ),
    );
    if (res.isEmpty) {
      return <ProductDetail>[];
    }
    return res.first.items ?? [];
  }

  Future<Iterable<ProductDetail>> _fetchFromPromotion(String event) async {
    talker.debug('[EfGridController] fetch promotion: $event');
    try {
      final res = await wabowProvider.getPromotionsBuyNGiftMWithId(
        event,
        PromotionIdReq(
          page: page,
          size: _countPerPage,
        ),
      );
      final products = res.products ?? [];
      final tabs = products.fold({}, (previousValue, product) {
        final map = product.category ?? {};
        for (final entity in map.entries) {
          if (entity.value == null || entity.value.isEmpty) {
            map[entity.key] = ' ';
          }
        }
        previousValue.addAll(product.category ?? {});
        return previousValue;
      });
      _promotion.value = res.promotion ?? Promotion();
      _tabs.assignAll(tabs);

      // Recreate TabController when tabs change
      _tabController.value?.dispose();
      _tabController.value = null;

      return products;
    } catch (e, s) {
      talker.error(e.toString(), e, s);
    }

    final res = await wabowProvider.getPromotionsBuyNGetMDiscountWithId(
      event,
      PromotionIdReq(
        page: page,
        size: _countPerPage,
      ),
    );
    final products = res.products ?? [];
    final tabs = products.fold({}, (previousValue, product) {
      previousValue.addAll(product.category ?? {});
      return previousValue;
    });
    _promotion.value = res.promotion ?? Promotion();
    _tabs.assignAll(tabs);

    // Recreate TabController when tabs change
    _tabController.value?.dispose();
    _tabController.value = null;

    return products;
  }

  Future<Iterable<ProductDetail>> _fetch() async {
    // /category/1
    if (Routes.CATEGORY == '/${url.action}') {
      return _fetchFromCategory(url.id ?? '');
    }
    // /promotion/1
    if (Routes.PROMOTION == '/${url.action}') {
      return _fetchFromPromotion(url.id ?? '');
    }
    try {
      final res = await _fetchFromPromotion(id);
      if (res.isNotEmpty) {
        return res;
      }
    } catch (e) {
      talker.error(e.toString());
    }
    try {
      final res = await _fetchFromCategory(id);
      if (res.isNotEmpty) {
        return res;
      }
    } catch (e) {
      talker.error(e.toString());
    }
    return [];
  }

  // 取得分類廣告
  Future<void> _fetchCategorySeo() async {
    try {
      // final box = boxProvider.getGsBox(Boxes.categorySeo.name);
      // final json = box.read(id);
      // final res = CategoriesSeoRes.fromJson(json);
      // final res = await wabowProvider.getCategoriesSeo(id);
      // headers.assignAll(res.adv?.products ?? []);
      final box =
          boxProvider.getGsBox(Boxes.advsCategories.name, withNamespace: false);
      if (box.hasData(id)) {
        final json = box.read(id);
        final category = Category.fromJson(json);
        appHeaders.assignAll(category.app ?? []);
      }
    } catch (e) {
      talker.error(e.toString());
    }
  }

  Future<void> _fetchCategories() async {
    try {
      final res = await wabowProvider.getCategories();
      _categories.assignAll(res);
    } catch (e, s) {
      talker.error(e.toString(), e, s);
    }
  }

  Future<void> onRefresh(String event) async {
    // log category id
    talker.debug('set id event: $event');
    talker.debug('url category: ${url.category}');
    try {
      await _fetchCategorySeo();
      await _fetchCategories();
      _page.value = 1;
      final it = await _fetch();
      if (it.length >= _countPerPage) {
        // next page
        _page.value++;
      } else {
        // end of page
        _page.value = 0;
      }
      products.assignAll(it);
      // GA event: log view item list
      await _logViewItemList();
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  ///
  /// GA event: log view item list
  ///
  Future<void> _logViewItemList() async {
    try {
      await FirebaseAnalytics.instance.logViewItemList(
        items: products
            .map((e) => e.toAnalyticsEventItem())
            .toList(growable: false),
        itemListId: id,
        itemListName: category?.name,
        // parameters:
      );
    } catch (e) {
      talker.error(e.toString());
    }
  }

  @override
  Future<void> onEndScroll() async {
    talker.info('onEndScroll');
    if (page == 0) {
      return;
    }
    try {
      final it = await _fetch();
      if (it.length >= _countPerPage) {
        // next page
        _page.value++;
      } else {
        // end of page
        _page.value = 0;
      }
      products.addAll(it);
    } catch (e) {
      _page.value = 0;
    }
  }

  @override
  Future<void> onTopScroll() async {
    // throw UnimplementedError();
  }
}
