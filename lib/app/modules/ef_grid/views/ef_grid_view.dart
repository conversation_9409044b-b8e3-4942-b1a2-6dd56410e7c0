import 'package:efshop/app/components/background.dart';
import 'package:efshop/app/components/ef_grid_item.dart';
import 'package:efshop/app/components/ef_icon_button.dart';
import 'package:efshop/app/components/error_button.dart';
import 'package:efshop/app/components/thumbnail_image.dart';
import 'package:efshop/app/models/category.dart';
import 'package:efshop/app/models/product_detail.dart' show ProductDetail;
import 'package:efshop/app/models/url.dart';
import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';

import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';

import '../controllers/ef_grid_controller.dart';

class EfGridView extends GetView<EfGridController> {
  final String? tag;

  EfGridView({
    super.key,
    Url? url,
    Category? category,
  }) : tag = url?.hashCode.toString() {
    Get.lazyPut(
      () => EfGridController(
        wabowProvider: Get.find(),
      ),
      tag: tag,
      fenix: true,
    );
    if (url != null) {
      initializeController().then((value) {
        final controller = Get.find<EfGridController>(tag: tag);
        controller.talker.debug('url: ${url.uri}');
        controller.url = url;
        controller.category = category;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<EfGridController>(
      init: Get.find<EfGridController>(tag: tag),
      tag: tag,
      initState: (_) {},
      builder: (controller) {
        return controller.obx(
          (state) {
            return Scaffold(
              appBar: AppBar(
                toolbarHeight: 0,
                bottom: _preferredSizeWidget(),
              ),
              body: _body(),
            );
          },
          onError: (err) => ErrorButton(
            err,
            onTap: () {
              controller.onRefresh(controller.id);
            },
          ),
        );
      },
    );
  }

  PreferredSizeWidget? _preferredSizeWidget() {
    final promotion = controller.promotion;
    if (promotion.hasThreshold) {
      return null;
    }
    if (controller.tabs.isEmpty) {
      return null;
    }
    return PreferredSize(
      preferredSize: const Size.fromHeight(Constants.tabHeight),
      child: Align(
        alignment: Alignment.centerLeft,
        child: _tabBar(),
      ),
    );
  }

  PreferredSizeWidget _tabBar() {
    return TabBar(
      indicator: const BoxDecoration(
        color: Colors.transparent, // 指示器的顏色
      ),
      labelStyle: const TextStyle(
        fontSize: 13,
      ),
      unselectedLabelStyle: const TextStyle(
        fontSize: 13,
      ),
      controller: controller.tabController,
      isScrollable: true,
      indicatorWeight: 0,
      tabs: _tabs().toList(growable: false),
      padding: EdgeInsets.zero,
      labelPadding: const EdgeInsets.symmetric(
        horizontal: 8,
      ),
      onTap: (value) {
        try {
          controller.pageController.jumpToPage(value);
        } catch (e, s) {
          controller.talker.error(e.toString(), e, s);
        }
      },
    );
  }

  Iterable<Widget> _tabs() {
    return controller.tabs.map((e) => Tab(text: e.value));
  }

  Widget _body() {
    final promotion = controller.promotion;
    return Background(
      alignment: Alignment.bottomRight,
      background: _background(),
      child: promotion.hasThreshold ? Obx(() => _floatingPanel()) : _button(),
    );
  }

  Widget _floatingPanel() {
    return AnimatedContainer(
      duration: Duration(milliseconds: 200),
      height: controller.panelHeight,
      child: _panel(),
    );
  }

  Widget _button() {
    return EfIconButton(
      onPressed: () {
        controller.scroll.jumpTo(0);
        controller.panelHeight = 0;
      },
      icon: SvgPicture.asset('assets/images/icon_expand_less.svg'),
      size: 38.dw,
      backgroundColor: const Color(0xfff2f2f5),
    ).paddingOnly(
      bottom: 8.dh,
      right: 8.dw,
    );
  }

  Widget _background() {
    final it = controller.tabs;
    if (it.isEmpty) {
      return Obx(() => _page(0));
    }
    return PageView.builder(
      physics: const NeverScrollableScrollPhysics(),
      controller: controller.pageController,
      itemCount: it.length,
      itemBuilder: (context, index) {
        controller.talker.debug('itemBuilder index: $index');
        return Obx(() => _page(index));
      },
      onPageChanged: (value) {
        controller.talker.debug('value: $value');
        try {
          controller.tabController?.animateTo(value);
        } catch (e, s) {
          controller.talker.error(e.toString(), e, s);
        }
      },
    );
  }

  Widget _page(int index) {
    return CustomScrollView(
      controller: controller.scroll,
      slivers: _slivers(index).toList(growable: false),
    );
  }

  Widget _panel() {
    Iterable<Widget> children() sync* {
      final promotion = controller.promotion;
      if (promotion.hasThreshold) {
        yield SizedBox(height: 10.dh);
        yield Padding(
          padding: EdgeInsets.symmetric(horizontal: 12),
          child: Obx(() {
            final remainCount = controller.remainCount;
            if (remainCount == 0) {
              return const Text(
                '已達優惠數量',
                style: TextStyle(
                  fontSize: 16,
                  color: EfColors.gray94,
                ),
                textAlign: TextAlign.left,
              );
            }
            return RichText(
              maxLines: 1,
              softWrap: false,
              text: TextSpan(
                text: '還差',
                style: const TextStyle(
                  fontSize: 18,
                  color: EfColors.gray94,
                ),
                children: [
                  TextSpan(
                    text: ' $remainCount ',
                    style: TextStyle(
                      color: EfColors.primary,
                    ),
                  ),
                  TextSpan(
                    text: '件享優惠',
                  ),
                ],
              ),
            );
          }),
        );
        yield SizedBox(height: 10.dh);
        yield Padding(
          padding: EdgeInsets.symmetric(horizontal: 12),
          child: TextButton(
            onPressed: _backToCart,
            style: TextButton.styleFrom(
              padding: EdgeInsets.zero,
              disabledBackgroundColor: EfColors.grayBB,
              backgroundColor: EfColors.primary,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(4)),
              ),
              // minimumSize: const Size.fromHeight(28),
            ),
            child: Text(
              '立即結帳',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        );
        yield SizedBox(height: 10.dh);
      }
    }

    return ColoredBox(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: children().toList(growable: false),
      ),
    );
  }

  ///
  /// 回到購物車
  ///
  Future<void> _backToCart() async {
    try {
      if (controller.prefProvider.cartQuantity > 0) {
        // 檢查是否登入
        final res = await getLoginRes();
        if (res == null || res.isExpired) {
          throw Exception('Login failed');
        }
      }
      await Get.offAllNamed(Routes.LOADING,
          parameters: <String, String>{Keys.id: '${IndexTab.cart.index}'});
    } catch (e) {
      controller.talker.error(e);
    }
  }

  Iterable<Widget> _slivers(int index) sync* {
    // log tab controller index
    // controller.talker
    //     .debug('_slivers index: ${controller.tabController.index}');
    // log page controller page
    // controller.talker.debug('page: ${controller.pageController.page}');
    // app headers
    if (controller.appHeaders.isNotEmpty) {
      final it = controller.appHeaders;
      yield SliverList.separated(
        itemBuilder: (context, index) {
          final data = it.elementAt(index);
          return ThumbnailImage(
            data.thumbnailByType,
            // enableVolume: true,
            onTap: data.url == null
                ? null
                : () {
                    if (Get.canLaunchUrl(data.url!.uri)) {
                      Get.launchUrl(data.url!.uri);
                    }
                  },
          );
        },
        separatorBuilder: (context, index) {
          return SizedBox(height: 10.dh);
        },
        itemCount: it.length,
      );
      yield SliverToBoxAdapter(
        child: SizedBox(height: 10.dh),
      );
    }
    // promotion headers
    final thumbnail = controller.promotion.thumbnail;
    final hasThumbnail = thumbnail != null && thumbnail.containsImage;
    if (hasThumbnail == true) {
      yield SliverToBoxAdapter(
        child: ThumbnailImage(
          thumbnail,
        ),
      );
      yield SliverToBoxAdapter(
        child: SizedBox(height: 10.dh),
      );
    }
    final promotion = controller.promotion;
    if (promotion.hasThreshold) {
      if (!hasThumbnail) {
        yield SliverToBoxAdapter(
          child: SizedBox(height: 10.dh),
        );
      }
      yield SliverPadding(
        padding: EdgeInsets.symmetric(
          horizontal: 12,
        ),
        sliver: SliverToBoxAdapter(
          child: promotion.isHtml
              ? HtmlWidget(
                  promotion.smallTitle ?? '',
                )
              : Text(
                  promotion.smallTitle ?? '',
                  style: const TextStyle(
                    fontSize: 14,
                    color: EfColors.gray94,
                  ),
                  textAlign: TextAlign.left,
                ),
        ),
      );
      yield SliverToBoxAdapter(
        child: SizedBox(height: 10.dh),
      );
      yield SliverPadding(
        padding: EdgeInsets.symmetric(horizontal: 12),
        sliver: SliverToBoxAdapter(
          child: Obx(() {
            final remainCount = controller.remainCount;
            if (remainCount == 0) {
              return const Text(
                '已達優惠數量',
                style: TextStyle(
                  fontSize: 16,
                  color: EfColors.gray94,
                ),
                textAlign: TextAlign.left,
              );
            }
            return RichText(
              maxLines: 1,
              softWrap: false,
              text: TextSpan(
                text: '還差',
                style: const TextStyle(
                  fontSize: 16,
                  color: EfColors.gray94,
                ),
                children: [
                  TextSpan(
                    text: ' $remainCount ',
                    style: TextStyle(
                      color: EfColors.primary,
                    ),
                  ),
                  TextSpan(
                    text: '件享優惠',
                    // style: const TextStyle(
                    //   fontSize: 16,
                    //   color: EfColors.gray94,
                    // ),
                  ),
                ],
              ),
            );
          }),
        ),
      );
      yield SliverToBoxAdapter(
        child: SizedBox(height: 10.dh),
      );
    }
    if (controller.headers.isNotEmpty) {
      final it = controller.headers;
      yield SliverList.builder(
        itemBuilder: (context, index) {
          final data = it.elementAt(index);
          return ThumbnailImage(
            data.thumbnail,
            onTap: () {
              final url = data.getUrl();
              final parameters = url.toJson();
              parameters.removeNull();
              Get.toNamed(
                url.action ?? '',
                parameters:
                    Map.castFrom<String, dynamic, String, String>(parameters),
              );
            },
          );
        },
        itemCount: it.length,
      );
      yield SliverToBoxAdapter(
        child: SizedBox(height: 10.dh),
      );
    }
    final it = controller.getCurrentProducts(index);
    final length = (it.length ~/ 2) + (it.length % 2);
    yield SliverList.separated(
      itemBuilder: (context, index) {
        if (index < length) {
          final leftIndex = index * 2;
          final left = it.elementAt(leftIndex);
          final rightIndex = leftIndex + 1;
          final right = rightIndex < it.length
              ? it.elementAt(rightIndex)
              : ProductDetail();
          return EfGridItem(
            left: left,
            right: right,
            wabowProvider: controller.wabowProvider,
          );
        }
        if (controller.page == 0) {
          return _endOfLine();
        }
        return _loadMore();
      },
      itemCount: length + 1,
      separatorBuilder: (context, index) {
        return SizedBox(height: 10.dh);
      },
    );
    yield SliverToBoxAdapter(
      child: Obx(() {
        return SizedBox(height: controller.panelHeight);
      }),
    );
  }

  // Widget _background2() {
  //   final it = controller.products;
  //   final length = (it.length ~/ 2);
  //   return ListView.separated(
  //     controller: controller.scroll,
  //     padding: EdgeInsets.symmetric(
  //       horizontal: 8.dw,
  //       vertical: 8.dh,
  //     ),
  //     itemBuilder: (context, index) {
  //       if (index < length) {
  //         final leftIndex = index * 2;
  //         final left = it.elementAt(leftIndex);
  //         final rightIndex = leftIndex + 1;
  //         final right = rightIndex < it.length
  //             ? it.elementAt(rightIndex)
  //             : ProductDetail();
  //         return EfGridItem(
  //           left: left,
  //           right: right,
  //         );
  //       }
  //       if (controller.page == 0) {
  //         return _endOfLine();
  //       }
  //       return _loadMore();
  //     },
  //     separatorBuilder: (context, index) {
  //       return SizedBox(height: 10.dh);
  //     },
  //     itemCount: length + 1,
  //   );
  // }

  Widget _loadMore() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _endOfLine() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.only(top: 8, bottom: 12),
        child: Row(
          children: [
            const Spacer(),
            const Expanded(
              child: Divider(
                color: EfColors.gray94,
              ),
            ),
            SizedBox(width: 18.dw),
            const Text(
              '已無更多商品',
              style: TextStyle(
                fontSize: 13,
                color: EfColors.gray94,
              ),
              textAlign: TextAlign.center,
              softWrap: false,
            ),
            SizedBox(width: 18.dw),
            const Expanded(
              child: Divider(
                color: EfColors.gray94,
              ),
            ),
            const Spacer(),
          ],
        ),
      ),
    );
  }

  // Widget _body() {
  //   final it = controller.products;
  //   return GridView.builder(
  //     controller: controller.scroll,
  //     padding: EdgeInsets.only(
  //       left: 8.dw,
  //       right: 8.dw,
  //       top: 16.dh,
  //     ),
  //     itemCount: it.length + controller.needLoadMore,
  //     gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
  //       crossAxisCount: 2, // 列數
  //       mainAxisSpacing: 10.dh, // 垂直間距
  //       crossAxisSpacing: 12.dw, // 水平間距
  //       childAspectRatio: SearchItem.aspectRatio,
  //     ),
  //     itemBuilder: (BuildContext context, int index) {
  //       if (index == it.length) {
  //         // 如果當前項目索引等於項目數量，則顯示加載更多的提示
  //         return const EfPlaceholder();
  //       }
  //       final data = it.elementAt(index);
  //       // 動態生成子元素
  //       return SearchItem(
  //         data,
  //         onPressed: () {
  //           final url = data.getUrl();
  //           final parameters = url.toJson();
  //           parameters.removeNull();
  //           Get.toNamed(
  //             url.action ?? '',
  //             parameters:
  //                 Map.castFrom<String, dynamic, String, String>(parameters),
  //           );
  //         },
  //       );
  //     },
  //   );
  // }
}
