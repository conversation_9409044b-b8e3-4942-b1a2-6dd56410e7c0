import 'dart:async';
import 'dart:convert';

import 'package:efshop/app/routes/app_pages.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

class EfWebController extends GetxController with StateMixin<String> {
  final _disposable = Completer();
  final Talker talker;
  late final WebViewController webViewController;
  // title
  final _title = ''.obs;
  String get title => _title.value;
  // url
  final _url = ''.obs;
  String get url => _url.value;
  set url(String value) => _url.value = value;
  // headers
  final headers = <String, String>{}.obs;

  EfWebController({
    required this.talker,
  });

  @override
  void onInit() {
    super.onInit();
    _initWebViewController();
    _url.stream
        .where((event) => event.isNotEmpty)
        .debounce(100.milliseconds)
        .asyncMap((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
    // fetchParameters();
  }

  void _initWebViewController() {
    late final PlatformWebViewControllerCreationParams params;
    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }
    webViewController = WebViewController.fromPlatformCreationParams(params);
    if (webViewController.platform is AndroidWebViewController) {
      // AndroidWebViewController.enableDebugging(true);
      (webViewController.platform as AndroidWebViewController)
          .setMediaPlaybackRequiresUserGesture(false);
    }
    webViewController.clearCache();
    webViewController.clearLocalStorage();
    webViewController.setJavaScriptMode(JavaScriptMode.unrestricted);
    webViewController.setNavigationDelegate(NavigationDelegate(
      // [EfWebController] onNavigationRequest: https://www.efshop.com.tw/content/1049/display?_gl=1*ix3rm8*_ga*MjYzMzcyMDM5LjE3MDU4OTEyMjY.*_ga_7WJCGM1SV3*MTcwNjc3ODM2OS4xNC4xLjE3MDY3Nzk3MjQuNDMuMC4w*_ga_595F0NH2M2*MTcwNjc3ODM3Mi4xNC4xLjE3MDY3Nzk3MjUuMC4wLjA.&_ga=2.45138467.626831679.1706681474-263372039.1705891226
      // [EfWebController] onNavigationRequest: https://m.me/EF.fansclub
      onNavigationRequest: (request) {
        final url = request.url;
        talker.info('[EfWebController] onNavigationRequest: $url');
        var uri = Uri.parse(url);
        // 特殊處理: 避免無限迴圈
        if (uri.path.startsWith(Routes.CONTENT)) {
          return NavigationDecision.navigate;
        }
        if (uri.scheme == Constants.schemeHttps) {
          return NavigationDecision.navigate;
        }

        // 特殊處理: line
        // intent://ti/p/@efshop#Intent;scheme=line;action=android.intent.action.VIEW;category=android.intent.category.BROWSABLE;package=jp.naver.line.android;end
        // ios: 1 - https://line.me/R/ti/p/@efshop
        // ios: 2 - line://ti/p/@efshop
        if (['intent', 'line'].contains(uri.scheme)) {
          var lineUri = uri;
          var needLaunch = false;
          if (uri.scheme == 'line') {
            // line://ti/p/@efshop
            needLaunch = true;
          } else if (uri.scheme == 'intent') {
            // 解析 Intent 參數
            Map<String, String> intentParams = {};
            String fragment = uri.fragment;
            if (fragment.startsWith('Intent;')) {
              List<String> parts = fragment.substring(7).split(';');
              for (String part in parts) {
                if (part == 'end') continue;
                List<String> keyValue = part.split('=');
                if (keyValue.length == 2) {
                  intentParams[keyValue[0]] = keyValue[1];
                }
              }
            }
            if (!intentParams.containsKey('scheme')) {
              // intent://pay/payment/K3EyVks5QnZyVnd3alNsdUlDS1NkMk54dkxtaHg2Qm02dlVCTVl3RStnWExvTHZabGZYUVJHcXdSY3dzRnZHbg
              lineUri = Uri(
                scheme: 'line',
                host: uri.host,
                path: uri.path,
              );
              needLaunch = true;
            }
          }
          if (needLaunch) {
            canLaunchUrl(lineUri).then((value) async {
              if (value) {
                await launchUrl(lineUri);
              }
            });
            return NavigationDecision.prevent;
          }
        }
        if (['intent'].contains(uri.scheme)) {
          // 解析 Intent 參數
          Map<String, String> intentParams = {};
          String fragment = uri.fragment;

          if (fragment.startsWith('Intent;')) {
            List<String> parts = fragment.substring(7).split(';');
            for (String part in parts) {
              if (part == 'end') continue;
              List<String> keyValue = part.split('=');
              if (keyValue.length == 2) {
                intentParams[keyValue[0]] = keyValue[1];
              }
            }

            // 取得目標 scheme
            if (intentParams.containsKey('scheme')) {
              final targetUri = Uri(
                  scheme: intentParams['scheme']!,
                  host: uri.host,
                  path: uri.path,
                  queryParameters: uri.queryParameters.isNotEmpty
                      ? uri.queryParameters
                      : null);

              talker.info('[EfWebController] 轉換 intent 為: $targetUri');

              // 檢查是否可以啟動此 URL
              canLaunchUrl(targetUri).then((value) async {
                if (value) {
                  await launchUrl(targetUri);
                } else {
                  talker.warning('[EfWebController] 無法啟動 URL: $targetUri');
                  // 如果有指定 package，可以嘗試開啟應用商店
                  if (intentParams.containsKey('package') &&
                      GetPlatform.isAndroid) {
                    final storeUri = Uri.parse(
                        'market://details?id=${intentParams['package']}');
                    if (await canLaunchUrl(storeUri)) {
                      await launchUrl(storeUri);
                    }
                  }
                }
                Get.back();
              });
              return NavigationDecision.prevent;
            }
          }
        }
        // 特殊處理: facebook
        // fb://profile/100064521766988?wtsid=wt_0TcyXMLZ0kcdacxnm
        if (uri.scheme == 'fb') {
          canLaunchUrl(uri).then((value) async {
            if (value) {
              await launchUrl(uri);
            }
          });
          return NavigationDecision.prevent;
        }
        if (Get.canLaunchUrl(uri)) {
          Get.launchUrl(uri);
          return NavigationDecision.prevent;
        }
        return NavigationDecision.navigate;
      },
      // [EfWebController] onUrlChange: https://m.efshop.com.tw/
      // [EfWebController] onUrlChange: https://m.efshop.com.tw/category_top
      // [EfWebController] onUrlChange: https://m.efshop.com.tw/member_center
      // [EfWebController] onUrlChange: https://m.efshop.com.tw/cart
      onUrlChange: (change) {
        final url = change.url;
        talker.info('[EfWebController] onUrlChange: $url');
        var uri = Uri.parse(change.url ?? '');
        // 特殊處理首頁
        if (uri.authority.endsWith(Constants.efDomain) ||
            uri.authority.isEmpty) {
          uri = uri.replace(path: uri.path == '/' ? Routes.HOME : uri.path);
          // 回到首頁的子頁面
          if (IndexTab.values
              .any((element) => uri.path.startsWith(element.value))) {
            Get.launchUrl(uri);
          }
        }
      },
    ));
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  void fetchParameters() {
    if (Get.parameters.containsKey(Keys.title)) {
      _title.value = Get.parameters[Keys.title] ?? '';
    }
    if (Get.parameters.containsKey(Keys.headers)) {
      final source = Get.parameters[Keys.headers] ?? '{}';
      final jsonObject = Map<String, String>.from(jsonDecode(source));
      headers.assignAll(jsonObject);
    }
    // {
    //   "action": "content",
    //   "url": "https://m.efshop.com.tw/content/1045/display",
    //   "url_mobile": "https://m.efshop.com.tw/content/1045/display"
    //   "url_app": "https://m.efshop.com.tw/app_access?redirect=content/1045/display"
    //   "content_main": "1045\/display"
    // }
    // 直接使用 app url 的方式
    if (Get.parameters.containsKey(Keys.urlApp)) {
      final urlString = Get.parameters[Keys.urlApp] ?? '';
      if (urlString.isNotEmpty) {
        url = urlString;
        return;
      }
    }
    if (Get.parameters.containsKey(Keys.url)) {
      final urlString = Get.parameters[Keys.url] ?? '';
      if (urlString.isNotEmpty) {
        // 使用正規表達式來匹配結尾是 "content/任意數字/display" 的 URL
        // 把 https://m.efshop.com.tw/content/1045/display 轉換成
        // https://m.efshop.com.tw/app_access?redirect=content/1045/display
        final pattern = RegExp(r'content/\d+/display$');
        // 使用正規表達式來檢查 urlString 是否匹配
        if (pattern.hasMatch(urlString)) {
          // 如果匹配，則 urlString 以 "content/任意數字/display" 結尾
          final uri = Uri.parse(urlString);
          final newUri = Uri.https(Constants.efAuthority, 'app_access', {
            'redirect': uri.pathSegments.join('/'),
          });
          url = newUri.toString();
        } else {
          url = urlString;
        }
        return;
      }
    }
    // /content/1049/display
    // {
    //   "id": "1049",
    //   "display": "display",
    // }
    if (Get.parameters.containsKey(Keys.id)) {
      final id = int.tryParse(Get.parameters[Keys.id] ?? '');
      if (id is int) {
        // final path = 'content/$id/display';
        // final uri = Uri.https(Constants.efAuthority, path);
        final uri = Uri.https(Constants.efAuthority, 'app_access', {
          'redirect': 'content/$id/display',
        });
        url = uri.toString();
        return;
      }
    }
    // /content/display?action=content&id=display&content_main=1045
    // {
    //   "id": "display",
    //   "content_main": "1045",
    //   "action": "content",
    // }
    // {
    //   "url": "https:\/\/m2.efshop.tt.wabow.com\/app_access?redirect=content\/1045\/display",
    //   "content_main": "1045\/display"
    //   "action": "content",
    // }
    if (Get.parameters.containsKey('content_main')) {
      final uri = Uri.parse(Get.parameters['content_main'] ?? '');
      if (uri.pathSegments.isNotEmpty) {
        final id = uri.pathSegments.first;
        final path = 'content/$id/display';
        final newUri = Uri.https(Constants.efAuthority, path);
        url = newUri.toString();
        return;
      }
    }
  }

  Future<void> _loadFromUrl() {
    return webViewController.loadRequest(
      Uri.parse(url),
      headers: headers,
    );
  }

  Future<void> _loadFromHtmlString() {
    return webViewController.loadHtmlString(url);
  }

  Future<void> onRefresh() async {
    try {
      talker.info('url: $url');
      final future =
          url.startsWith('http') ? _loadFromUrl : _loadFromHtmlString;
      await future();
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }
}
