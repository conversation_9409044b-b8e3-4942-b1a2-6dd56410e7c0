import 'package:efshop/app/components/action_button.dart';
import 'package:efshop/app/components/ef_popup.dart';
import 'package:efshop/app/modules/cart_web/views/cart_web_view.dart';
import 'package:efshop/app/modules/chat/views/chat_view.dart';
import 'package:efshop/app/modules/home/<USER>/home_view.dart';
import 'package:efshop/app/modules/order_fail/views/order_fail_view.dart';
import 'package:efshop/app/modules/order_success/views/order_success_view.dart';
import 'package:efshop/app/modules/profile/views/profile_view.dart';
import 'package:efshop/app/modules/show_empty_cart/views/show_empty_cart_view.dart';
import 'package:efshop/constants.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';

import '../../categories/views/categories_view.dart';
import '../controllers/index_controller.dart';

class IndexView extends GetView<IndexController> {
  const IndexView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // appBar: AppBar(
      //   title: const Text('HomeView'),
      //   centerTitle: true,
      // ),
      body: controller.obx((state) {
        return _pager();
        // return Obx(() => _body());
      }),
      // floatingActionButton: FloatingActionButton(
      //   onPressed: controller.increment,
      //   child: const Icon(Icons.add),
      // ),
      // floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      // 這個元件有挖空 floating button 的效果
      // bottomNavigationBar: Obx(() {
      //   return Visibility(
      //     visible: controller.currentTab != IndexTab.home,
      //     child: BottomAppBar(
      //       shape: const CircularNotchedRectangle(),
      //       child: Obx(() {
      //         return Row(
      //           mainAxisAlignment: MainAxisAlignment.spaceAround,
      //           children: _bottomButtons().toList(growable: false),
      //         );
      //       }),
      //     ),
      //   );
      // }),
      bottomNavigationBar: Obx(() {
        return Visibility(
          visible: Constants.visibleIndexTab.contains(controller.currentTab),
          child: BottomAppBar(
            shape: const CircularNotchedRectangle(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: _bottomButtons().toList(growable: false),
            ),
          ),
        );
      }),
    );
  }

  Future<void> _showPopUp() async {
    final data = await controller.membersPopupRes.future;
    if (data.voucherId is int) {
      await EfPopUp(
        data,
        onPressed: () {
          Get.back();
          var path = data.popupButtonPath ?? '';
          if (!path.startsWith('/')) {
            path = '/$path';
          }
          final uri = Uri.parse(path);
          if (Get.canLaunchUrl(uri)) {
            Get.launchUrl(uri);
          }
        },
      ).dialog();
      controller.putPopup(data.voucherId!);
    }
  }

  Widget _pager() {
    const it = IndexTab.values;
    if (it.isEmpty) {
      return const SizedBox();
    }
    return PageView.builder(
      physics: const NeverScrollableScrollPhysics(),
      controller: controller.pageController,
      itemCount: it.length,
      itemBuilder: (context, index) {
        final page = it.elementAt(index);
        return _page(page);
      },
    );
  }

  // tab 只有5個，但以下 page 有 7 個
  // 首頁
  // 分類
  // 客服
  // 會員
  // 結帳
  // 訂單成功
  // 訂單失敗
  // 空白
  Widget _page(IndexTab state) {
    // 首頁
    if (IndexTab.home == state) {
      _showPopUp();
      return HomeView(
        changeTab: _changeTabWithAuth,
      );
    }
    // 分類
    if (IndexTab.category == state) {
      return CategoriesView(
        changeTab: (tab) async {
          controller.currentTab = tab;
        },
      );
    }
    // 訂單結果
    if ([
      IndexTab.cartFinish, // 訂單完成
      IndexTab.appCartFinish, // App 訂單完成
    ].contains(state)) {
      // 取得訂單 id
      final orderId = Get.parameters[Keys.orderId] ?? '';
      // 取得訂單狀態
      final orderStatus = Get.parameters[Keys.orderStatus] ?? '';
      if (orderStatus == 'success') {
        return OrderSuccessView(
          changeTab: _changeTabWithAuth,
          orderId: orderId,
        );
      }
      if (orderStatus == 'fail') {
        return OrderFailView(
          changeTab: _changeTabWithAuth,
          orderId: orderId,
        );
      }
    }
    // 購物車
    if (IndexTab.cart == state) {
      // final headers = {
      //   'Authorization': 'Bearer ${controller.prefProvider.token}',
      //   'device_type': Platform.operatingSystem,
      //   'app_version': controller.prefProvider.packageInfo.version,
      // };
      // Get.parameters = {
      //   Keys.title: '購物車',
      //   Keys.url: Constants.uriCart.toString(),
      //   Keys.headers: jsonEncode(headers),
      // };
      // return EfWebView(
      //   tag: Keys.title,
      // );
      // return CartView(
      //   changeTab: _changeTabWithAuth,
      // );
      controller.talker.debug('[IndexView] CartWebView');
      return CartWebView(
        changeTab: _changeTabWithAuth,
      );
    }
    // 空購物車
    if (IndexTab.showEmptyCart == state) {
      return ShowEmptyCartView(
        changeTab: _changeTabWithAuth,
        getTab: () => controller.currentTab,
      );
    }
    // 會員
    if (IndexTab.profile == state) {
      return ProfileView(
        changeTab: _changeTabWithAuth,
      );
      // } else if (IndexTab.favorite == state) {
      //   return const FavoriteView();
    }
    // 客服
    if (IndexTab.service == state) {
      // return const Center(
      //   child: Text(
      //     // 'HomeView is working ${controller.count}',
      //     '客服',
      //     style: TextStyle(fontSize: 20),
      //   ),
      // );
      // final headers = {
      //   'Authorization': 'Bearer ${controller.prefProvider.token}',
      //   'device_type': Platform.operatingSystem,
      //   'app_version': controller.prefProvider.packageInfo.version,
      // };
      Get.parameters = {
        Keys.title: '客服',
        Keys.url: Constants.uriWebChat.toString(),
        // Keys.headers: jsonEncode(headers),
      };
      return ChatView();
    }
    return const SizedBox();
  }

  Iterable<Widget> _bottomButtons() sync* {
    // 首頁
    yield ActionButton.bottom(
      text: '首頁',
      icon: 'assets/images/home_outline.svg',
      selectedIcon: 'assets/images/home.svg',
      isSelected: controller.currentTab == IndexTab.home,
      onPressed: () => _changeTabWithAuth(IndexTab.home),
      // onPressed: () => controller.currentTab = IndexTab.home,
      badgeOffset: const Offset(4, 0),
    ).expanded();
    // 分類
    yield ActionButton.bottom(
      text: '分類',
      icon: 'assets/images/category.svg',
      selectedIcon: 'assets/images/category_fill.svg',
      isSelected: controller.currentTab == IndexTab.category,
      onPressed: () => _changeTabWithAuth(IndexTab.category),
      // onPressed: () => controller.currentTab = IndexTab.category,
      badgeOffset: const Offset(4, 0),
    ).expanded();
    // 客服
    yield ActionButton.bottom(
      text: '客服',
      icon: 'assets/images/customer_service.svg',
      isSelected: controller.currentTab == IndexTab.service,
      onPressed: () => _changeTabWithAuth(IndexTab.service),
      // onPressed: () => controller.currentTab = IndexTab.service,
      badgeOffset: const Offset(4, 0),
    ).expanded();
    // 會員
    yield ActionButton.bottom(
      text: '會員',
      icon: 'assets/images/profile_outline.svg',
      selectedIcon: 'assets/images/profile.svg',
      isSelected: controller.currentTab == IndexTab.profile,
      onPressed: () => _changeTabWithAuth(IndexTab.profile),
      // onPressed: () => controller.currentTab = IndexTab.profile,
      badgeOffset: const Offset(4, 0),
    ).expanded();
    // 收藏
    // yield ActionButton.bottom(
    //   text: '收藏',
    //   icon: 'assets/images/favorite_outline.svg',
    //   selectedIcon: 'assets/images/favorite.svg',
    //   // showBadge: true,
    //   // badgeCount: 0,
    //   isSelected: controller.currentTab == IndexTab.favorite,
    //   onPressed: () => _changeTabWithAuth(IndexTab.favorite),
    // ).expanded();
    // 結帳
    yield ActionButton.bottom(
      text: '結帳',
      icon: 'assets/images/cart.svg',
      badgeCount: controller.prefProvider.cartQuantity,
      isSelected: controller.currentTab == IndexTab.cart,
      onPressed: () => _changeTabWithAuth(IndexTab.cart),
      // onPressed: () => controller.currentTab = IndexTab.cart,
      // onPressed: () {
      //   Get.toNamed(Routes.CART);
      // },
      badgeOffset: const Offset(4, 0),
    ).expanded();
  }

  bool _needAuthorization(IndexTab tab) {
    if (tab == IndexTab.profile) {
      return true;
    }
    if (tab == IndexTab.cart) {
      if (controller.prefProvider.cartQuantity > 0) {
        return true;
      }
    }
    return false;
  }

  Future<void> _changeTabWithAuth(IndexTab tab) async {
    try {
      var authorized = true;
      if (_needAuthorization(tab)) {
        final res = await getLoginRes();
        if (res == null || res.isExpired == true) {
          authorized = false;
        }
      }
      if (authorized) {
        controller.currentTab = tab;
      }
    } catch (e) {
      controller.talker.error(e);
    }
  }
}
