import 'package:efshop/app/modules/ef_grid/views/ef_grid_view.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';

import '../controllers/sale_controller.dart';

class SaleView extends GetView<SaleController> {
  final ValueSetter<double>? onOverscroll;

  const SaleView({
    super.key,
    this.onOverscroll,
  });

  @override
  Widget build(BuildContext context) {
    Get.lazyPut<SaleController>(
      () => SaleController(
        wabowProvider: Get.find(),
      ),
      fenix: true,
    );
    return GetBuilder<SaleController>(
      init: Get.find<SaleController>(),
      builder: (_) {
        return controller.obx(
          (state) {
            return _main();
          },
        );
      },
    );
  }

  Widget _main() {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        toolbarHeight: 0,
        bottom: _preferredSizeWidget(),
      ),
      body: NotificationListener<OverscrollNotification>(
        onNotification: (notification) {
          // controller.talker.info('notification: $notification');
          final overscroll = notification.overscroll;
          final childController = controller.pageController;
          if (overscroll > 0 && childController.position.isAtBottom) {
            onOverscroll?.call(overscroll);
          } else if (overscroll < 0 && childController.position.isAtTop) {
            onOverscroll?.call(overscroll);
          }
          return false;
        },
        child: _body(),
      ),
    );
  }

  PreferredSizeWidget? _preferredSizeWidget() {
    if (controller.tabs.isEmpty) {
      return null;
    }
    return PreferredSize(
      preferredSize: const Size.fromHeight(48),
      child: Align(
        alignment: Alignment.centerLeft,
        child: _tabBar(),
      ),
    );
  }

  PreferredSizeWidget _tabBar() {
    return TabBar(
      indicator: const BoxDecoration(
        color: EfColors.primary, // 指示器的顏色
      ),
      controller: controller.tabController,
      isScrollable: true,
      tabs: _tabs().toList(growable: false),
      padding: EdgeInsets.zero,
      labelPadding: EdgeInsets.zero,
      labelColor: Colors.white,
      unselectedLabelColor: EfColors.gray,
      labelStyle: const TextStyle(
        fontSize: 13,
      ),
      unselectedLabelStyle: const TextStyle(
        fontSize: 13,
      ),
      // tabAlignment: TabAlignment.start,
      onTap: (value) {
        controller.pageController.jumpToPage(value);
      },
    );
  }

  Iterable<Widget> _tabs() {
    return controller.tabs.map((entry) {
      return Tab(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            minWidth: 100.dw,
            minHeight: 52.dh,
          ),
          child: Padding(
            padding: EdgeInsets.only(top: 3.dh, left: 5.dw, right: 5.dw),
            child: Text(
              entry.name ?? '',
              // style: const TextStyle(
              //   fontSize: 13,
              //   color: Colors.white,
              // ),
              softWrap: false,
              textAlign: TextAlign.center,
              overflow: TextOverflow.visible,
            ),
          ),
        ),
      );
    });
  }

  Widget _body() {
    final it = controller.tabs;
    if (it.isEmpty) {
      return const SizedBox();
    }
    return PageView.builder(
      physics: const ClampingScrollPhysics(),
      controller: controller.pageController,
      itemCount: it.length,
      onPageChanged: (value) {
        try {
          controller.tabController?.animateTo(value);
        } catch (e) {
          controller.talker.error(e);
        }
      },
      itemBuilder: (context, index) {
        final category = it.elementAt(index);
        return EfGridView(
          url: category.url,
          category: category,
        );
      },
    );
  }
}
