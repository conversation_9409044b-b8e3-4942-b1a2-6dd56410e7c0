import 'dart:async';

import 'package:efshop/app/models/members_orders_res.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:efshop/keys.dart';
import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';
import 'package:stream_transform/stream_transform.dart';

class OrderDetailController extends GetxController with StateMixin<String> {
  final WabowProvider wabowProvider;
  Talker get talker => wabowProvider.talker;
  BoxProvider get boxProvider => wabowProvider.boxProvider;
  final _disposable = Completer();
  final _id = ''.obs;
  String get id => _id.value;
  final _data = MembersOrdersRes().obs;
  MembersOrdersRes get data => _data.value;

  OrderDetailController({
    required this.wabowProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _id.stream
        .tap((p0) => change('', status: RxStatus.loading()))
        .asyncMap((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
    final box = boxProvider.getGsBox(Boxes.order.name);
    box
        .watch()
        .debounce(200.milliseconds)
        .takeUntil(_disposable.future)
        .listen((event) {
      talker.info('[OrderDetailController] order box changed');
      try {
        final json = box.read(id);
        if (json == null) {
          throw Exception('order box read null');
        }
        _data.value = MembersOrdersRes.fromJson(json);
      } catch (e) {
        talker.error(e);
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
    if (Get.parameters.containsKey(Keys.id)) {
      _id.value = Get.parameters[Keys.id] ?? '';
    }
    // onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      _data.value = await wabowProvider.getMembersOrdersWithId(id);
      // await _logPurchase(data);
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  ///
  /// GA: log purchase
  ///
  // Future<void> _logPurchase(MembersOrdersRes order) async {
  //   try {
  //     await FirebaseAnalytics.instance.logPurchase(
  //       currency: 'TWD',
  //       // coupon: order.couponCode,
  //       value: order.subtotal?.toDouble(),
  //       // items: children().toList(growable: false),
  //       items: order.normalProducts
  //           .map((e) => e.toAnalyticsEventItem())
  //           .toList(growable: false),
  //       // tax: order.tax?.toDouble(),
  //       shipping: order.shippingFee?.toDouble(),
  //       transactionId: order.number,
  //       affiliation: order.affiliateOrderType,
  //       // parameters:
  //       // callOptions: // web only
  //     );
  //   } catch (e) {
  //     talker.error(e);
  //   }
  // }
}
