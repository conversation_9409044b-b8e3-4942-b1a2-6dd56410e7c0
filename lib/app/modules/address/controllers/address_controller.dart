import 'dart:async';

import 'package:efshop/app/models/members_address_res.dart';
import 'package:efshop/app/models/message_res.dart';
import 'package:efshop/app/providers/address_provider.dart';
import 'package:efshop/app/providers/box_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/enums.dart';
import 'package:efshop/extension.dart';
import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';
import 'package:stream_transform/stream_transform.dart';

class AddressController extends GetxController with StateMixin<String> {
  static const _maxAddress = 3;

  final _disposable = Completer();
  final tabs = [
    AddressType.sevenEleven,
    AddressType.familyMart,
    AddressType.home,
  ].obs;

  final _currentTab = AddressType.sevenEleven.obs;
  AddressType get currentTab => _currentTab.value;
  set currentTab(AddressType value) => _currentTab.value = value;

  final AddressProvider addressProvider;
  WabowProvider get wabowProvider => addressProvider.wabowProvider;
  BoxProvider get boxProvider => wabowProvider.prefProvider.boxProvider;
  Talker get talker => wabowProvider.talker;

  Iterable<MembersAddressRes> get data => getAddressList(currentTab.value);

  bool get canCreate => data.length < _maxAddress;

  AddressController({
    required this.addressProvider,
  });

  @override
  void onInit() {
    super.onInit();
    final box = boxProvider.getGsBox(Boxes.address.name);
    box
        .watch()
        .debounce(200.milliseconds)
        .takeUntil(_disposable.future)
        .listen((event) {
      talker.info('address box changed');
      _currentTab.refresh();
    });
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      await addressProvider.fetchAddress();
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  Future<MessageRes> deleteAddress(MembersAddressRes data) async {
    final res = await wabowProvider.deleteMembersAddresses(data.id ?? '');
    if (res.status == true) {
      final box = boxProvider.getGsBox(Boxes.address.name);
      await box.remove(data.id ?? '');
    }
    return res;
  }

  Future<MessageRes> makeDefaultAddress(MembersAddressRes data) async {
    final res = await addressProvider.makeDefaultAddress(currentTab, data);
    if (res.status == true) {
      await addressProvider.fetchAddress();
    }
    return res;
  }

  Iterable<MembersAddressRes> getAddressList(String type) {
    return addressProvider.getAddressList((element) => element.type == type);
  }
}
