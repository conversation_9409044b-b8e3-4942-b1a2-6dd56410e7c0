import 'package:efshop/app/components/activity_item.dart';
import 'package:efshop/app/components/ef_center.dart';
import 'package:efshop/app/components/error_button.dart';
import 'package:efshop/app/models/url.dart';
import 'package:efshop/ef_colors.dart';
import 'package:efshop/extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';

import '../controllers/activity_controller.dart';

class ActivityView extends GetView<ActivityController> {
  const ActivityView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('優惠活動'),
        centerTitle: true,
      ),
      body: controller.obx(
        (state) {
          return SafeArea(
            child: Obx(() => _body()),
          );
        },
        onEmpty: _empty(),
        onError: (err) => ErrorButton(
          err,
          onTap: () {
            controller.onRefresh();
          },
        ),
      ),
    );
  }

  Widget _empty() {
    Iterable<Widget> children() sync* {
      yield SvgPicture.asset(
        'assets/images/big_star.svg',
        width: 50,
        height: 46,
        fit: BoxFit.contain,
      );
      yield const SizedBox(height: 20);
      yield const Text(
        '目前沒有活動',
        style: TextStyle(
          fontSize: 13,
          color: EfColors.gray93,
        ),
        softWrap: false,
      );
    }

    return EfCenter(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: children().toList(growable: false),
      ),
    );
  }

  Widget _body() {
    return ListView.separated(
      padding: const EdgeInsets.all(14),
      itemBuilder: (context, index) {
        final data = controller.data.elementAt(index);
        return ActivityItem(
          data,
          onPressed: () async {
            try {
              final url = data.url ?? Url();
              await Get.launchUrl(url.uri);
            } catch (e) {
              controller.talker.error(e);
            }
          },
        );
      },
      separatorBuilder: (context, index) {
        return const SizedBox(height: 14);
      },
      itemCount: controller.data.length,
    );
  }
}
