import 'package:efshop/extension.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_share_me/flutter_share_me.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

import '../models/product_detail.dart';
import 'ef_icon_button.dart';

class EfShare extends StatelessWidget {
  final ProductDetail product;
  final FlutterShareMe flutterShareMe;
  final Talker talker;

  String get shareUrl => product.shareUrl ?? '';
  String get msg => product.name ?? '';

  const EfShare(
    this.product, {
    super.key,
    required this.talker,
    required this.flutterShareMe,
  });

  @override
  Widget build(BuildContext context) {
    return _body();
  }

  Widget _body() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield SizedBox(
      // height: 60.dh,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: _buttons().toList(growable: false),
      ),
    );
    yield SizedBox(height: 20.dh);
    yield EfIconButton(
      icon: DecoratedBox(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: Colors.white,
            width: 1,
          ),
        ),
        child: Icon(
          Icons.close,
          size: 30.dw,
          color: Colors.white,
        ),
      ),
      onPressed: () {
        Get.back();
      },
    );
    yield SizedBox(height: 20.dh);
  }

  ///
  /// clip board
  ///
  Widget _shareLinkButton() {
    return EfIconButton(
      onPressed: () async {
        try {
          Get.back();
          await Clipboard.setData(ClipboardData(text: shareUrl));
          // SocialShare.copyToClipboard(text: shareUrl);
          // final response = await flutterShareMe.shareToSystem(msg: shareUrl);
          await Get.showToast('連結已複製');
          // GA: log share
          await _logShare();
        } catch (e) {
          Get.showAlert(e.toString());
        }
      },
      icon: SizedBox.square(
        dimension: 34.dw,
        child: SvgPicture.asset('assets/images/share_link.svg'),
      ),
      text: const Text(
        '複製連結',
        style: TextStyle(
          fontSize: 13,
          color: Colors.white,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  ///
  /// Messenger
  ///
  Widget _shareMessengerButton() {
    return EfIconButton(
      onPressed: () async {
        try {
          Get.back();
          final url = 'fb-messenger://share?link=${Uri.encodeFull(shareUrl)}';
          final uri = Uri.parse(url);
          await launchUrl(uri);
          // GA: log messenger share
          await _logMessengerShare();
          // if (await canLaunchUrl(uri)) {
          //   await launchUrl(uri);
          // } else {
          //   throw '未安裝 Facebook Messenger';
          // }
          // final response =
          //     await flutterShareMe.shareToMessenger(url: shareUrl, msg: '');
        } catch (e) {
          // Get.showAlert(e.toString());
          Get.showAlert('未安裝 Facebook Messenger');
        }
      },
      icon: SizedBox.square(
        dimension: 34.dw,
        child: SvgPicture.asset('assets/images/share_messenger.svg'),
      ),
      text: const Text(
        'Messenger',
        style: TextStyle(
          fontSize: 13,
          color: Colors.white,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  ///
  /// Facebook
  ///
  Widget _shareFacebookButton() {
    return EfIconButton(
      onPressed: () async {
        try {
          Get.back();
          // final res = await SocialShare.shareFacebookStory(
          //   appId: Constants.facebookId,
          //   attributionURL: shareUrl,
          // );
          final response =
              await flutterShareMe.shareToFacebook(msg: msg, url: shareUrl);
          talker.info('shareToFacebook: $response');
          // GA: log facebook share
          await _logFacebookShare();
        } catch (e) {
          Get.showAlert(e.toString());
        }
      },
      icon: SizedBox.square(
        dimension: 34.dw,
        child: SvgPicture.asset('assets/images/share_facebook.svg'),
      ),
      text: const Text(
        'Facebook',
        style: TextStyle(
          fontSize: 13,
          color: Colors.white,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  ///
  /// LINE
  ///
  Widget _shareLineButton() {
    return EfIconButton(
      onPressed: () async {
        try {
          Get.back();
          final url = 'line://msg/text/${Uri.encodeFull(shareUrl)}';
          final uri = Uri.parse(url);
          await launchUrl(uri);
          // if (await canLaunchUrl(uri)) {
          //   await launchUrl(uri);
          // } else {
          //   throw '未安裝 LINE';
          // }
          // GA: log line share
          await _logLineShare();
        } catch (e) {
          // Get.showAlert(e.toString());
          Get.showAlert('未安裝 LINE');
        }
      },
      icon: SizedBox.square(
        dimension: 34.dw,
        child: SvgPicture.asset('assets/images/share_line.svg'),
      ),
      text: const Text(
        'LINE',
        style: TextStyle(
          fontSize: 13,
          color: Colors.white,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Iterable<Widget> _buttons() sync* {
    yield _shareLinkButton().expanded();
    yield _shareMessengerButton().expanded();
    yield _shareFacebookButton().expanded();
    yield _shareLineButton().expanded();
  }

  ///
  /// GA: log line share
  ///
  Future<void> _logLineShare() async {
    try {
      await FirebaseAnalytics.instance.logShare(
        contentType: 'product',
        itemId: product.number ?? '',
        method: 'line',
      );
    } catch (e) {
      talker.error(e);
    }
  }

  ///
  /// GA: log facebook share
  ///
  Future<void> _logFacebookShare() async {
    try {
      await FirebaseAnalytics.instance.logShare(
        contentType: 'product',
        itemId: product.number ?? '',
        method: 'facebook',
      );
    } catch (e) {
      talker.error(e);
    }
  }

  ///
  /// GA: log share
  ///
  Future<void> _logShare() async {
    try {
      await FirebaseAnalytics.instance.logShare(
        contentType: 'product',
        itemId: product.number ?? '',
        method: 'link',
      );
    } catch (e) {
      talker.error(e);
    }
  }

  ///
  /// GA: log messenger share
  ///
  Future<void> _logMessengerShare() async {
    try {
      await FirebaseAnalytics.instance.logShare(
        contentType: 'product',
        itemId: product.number ?? '',
        method: 'messenger',
      );
    } catch (e) {
      talker.error(e);
    }
  }
}
