import 'dart:async';
import 'dart:convert';
import 'dart:developer' show log;

import 'package:appier_flutter/appier_flutter.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:efshop/app/providers/address_provider.dart';
import 'package:efshop/app/providers/appier_provider.dart';
import 'package:efshop/app/providers/wabow_provider.dart';
import 'package:efshop/constants.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_line_sdk/flutter_line_sdk.dart';
import 'package:flutter_share_me/flutter_share_me.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'package:package_info_plus/package_info_plus.dart';
import 'package:sizer/sizer.dart';
import 'package:talker_dio_logger/talker_dio_logger.dart';
import 'package:talker_flutter/talker_flutter.dart';

import 'app/models/notification_message_data.dart';
import 'app/providers/api_provider.dart';
import 'app/providers/bank_provider.dart';
import 'app/providers/box_provider.dart';
import 'app/providers/love_code_provider.dart';
import 'app/providers/message_provider.dart';
import 'app/providers/notification_provider.dart';
import 'app/providers/pref_provider.dart';
import 'app/routes/app_pages.dart';
import 'crashlytics_talker_observer.dart';
import 'firebase_options.dart';
import 'keys.dart';

void main() {
  // it should be the first line in main method
  WidgetsFlutterBinding.ensureInitialized();
  // 使用场景:后台状态的轻量级消息处理,无法直接访问 UI。
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  // await service initialized
  _init().then((value) => _run()).catchError((error) => log(error));
}

void _run() {
  runApp(
    ResponsiveSizer(
      builder: (context, orientation, screenType) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaler: const TextScaler.linear(1.1),
            // boldText: false,
          ),
          child: GetMaterialApp(
            title: "衣芙",
            navigatorKey: Get.key,
            initialRoute: AppPages.INITIAL,
            getPages: AppPages.routes,
            theme: Constants.themeData,
            localizationsDelegates: [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('zh', 'TW'),
              Locale('en', 'US'),
            ],
            locale: const Locale('zh', 'TW'),
          ),
        );
      },
    ),
  );
}

Future<void> _initFirebaseApp(bool sandbox) async {
  if (Firebase.apps.isEmpty) {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.getCurrentPlatform(sandbox),
    );
  }
  // Pass all uncaught "fatal" errors from the framework to Crashlytics
  FlutterError.onError = (flutterErrorDetails) {
    // if (flutterErrorDetails.library == "image resource service" &&
    //     flutterErrorDetails.exception
    //         .toString()
    //         .startsWith("HttpException: Invalid statusCode: 404, uri")) {
    //   return;
    // }
    if (kReleaseMode) {
      FirebaseCrashlytics.instance.recordFlutterFatalError(flutterErrorDetails);
    } else {
      log(
        flutterErrorDetails.toString(),
        error: flutterErrorDetails.exception,
        stackTrace: flutterErrorDetails.stack,
      );
    }
  };
  // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
  PlatformDispatcher.instance.onError = (error, stack) {
    if (kReleaseMode) {
      FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    } else {
      log('Error: $error', error: error, stackTrace: stack);
    }
    return true;
  };
}

Future<void> _initFirebase() async {
  final prefProvider = Get.find<PrefProvider>();
  await _initFirebaseApp(prefProvider.isSandBox);
  Get.lazyPut(() => FirebaseAnalytics.instance, fenix: true);
  final analytics = FirebaseAnalytics.instance;
  await analytics.setDefaultEventParameters({
    'version': prefProvider.packageInfo.version,
    'build_number': prefProvider.packageInfo.buildNumber,
    'package_name': prefProvider.packageInfo.packageName,
    'sandbox': prefProvider.isSandBox ? 1 : 0,
  });
  await analytics.logAppOpen();
  // messaging
  final messaging = FirebaseMessaging.instance;
  // 取得推播權限
  final settings = await messaging.requestPermission(
    alert: true,
    badge: true,
    sound: true,
  );
  final talker = prefProvider.talker;
  talker.info('User granted permission: ${settings.authorizationStatus}');
  // 取得推播 token
  try {
    final fcmToken = await messaging.getToken();
    talker.info('firebase messaging token: $fcmToken');
    prefProvider.fcmToken = fcmToken ?? '';
    if (fcmToken != null && fcmToken.isNotEmpty) {
      await AppierFlutter.setFcmToken(fcmToken);
    }
  } catch (e) {
    talker.error(e.toString());
  }
  // 取得 device token
  try {
    final apnsToken = await messaging.getAPNSToken();
    talker.info('apns token: $apnsToken');
    prefProvider.apnsToken = apnsToken ?? '';
  } catch (e) {
    talker.error(e.toString());
  }
}

/// 由於處理程序在應用程式上下文之外以自己的隔離方式運行，
/// 因此無法更新應用程式狀態或執行任何影響 UI 的邏輯。
/// 但是，您可以執行 HTTP 請求等邏輯、執行 IO 操作（例如更新本機儲存）、
/// 與其他外掛程式通訊等。
///
/// 也建議盡快完成你的邏輯。運行長時間、密集的任務會影響設備效能，
/// 並可能導致作業系統終止進程。如果任務運行時間超過 30 秒，設備可能會自動終止該進程。
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  final jsonString = jsonEncode(message.toMap());
  Get.lazyPut(
    () => TalkerFlutter.init(
      observer: CrashlyticsTalkerObserver(),
    ),
    fenix: true,
  );
  final talker = Get.find<Talker>();
  talker.info("[main] Handling a background message: $jsonString");

  await Hive.initFlutter();
  final userDefault = await Hive.openBox(Keys.userDefault);
  Get.lazyPut(() => BoxProvider(userDefault), fenix: true);
  Get.putAsync(() => PackageInfo.fromPlatform(), permanent: true);
  Get.lazyPut(() => DeviceInfoPlugin(), fenix: true);
  Get.lazyPut(
    () => PrefProvider(
      deviceInfo: Get.find(),
      packageInfo: Get.find(),
      boxProvider: Get.find(),
      talker: Get.find(),
    ),
    fenix: true,
  );
  // If you're going to use other Firebase services in the background, such as Firestore,
  // make sure you call `initializeApp` before using other Firebase services.
  // PrefProvider 尚未初始
  final prefProvider = Get.find<PrefProvider>();
  await _initFirebaseApp(prefProvider.isSandBox);
  await _initAppier();
  talker.debug("[main] Handling a background message: ${message.messageId}");
  final from = message.from;
  talker.debug("[main] Message from: $from");
  final data = message.data;
  final dataString = jsonEncode(data);
  // [main] Message data: {"sound":"1","icon":"myicon","body":"抗UV吸排抗菌運動衣褲！120元起","title":"抗UV吸排抗菌運動衣褲！120元起","parameters":"{\"partner_id\":\"home\",\"action\":\"category\",\"id\":\"457\",\"type\":\"activities\"}"}
  talker.debug("[main] Message data: $dataString");
  // final isAppierPush = await AppierFlutter.isAppierPush(dataString);
  final isAppierPush = data.containsKey('qg');
  if (isAppierPush || await AppierFlutter.isAppierPush(dataString)) {
    try {
      await AppierFlutter.handleRemoteMessage(dataString);
    } catch (e) {
      talker.error(e.toString());
    }
  } else {
    try {
      final data = NotificationMessageData.fromJson(message.data);
      // [main] notification message title: 寵愛爸比！涼感神T【買一送一】滿額再折８８元
      talker.info("[main] notification message title: ${data.title}");
      // [main] notification message body: 寵愛爸比！涼感神T【買一送一】滿額再折８８元
      talker.info("[main] notification message body: ${data.body}");
      final notificationProvider = await _initNotificationProvider(Get.find());
      await notificationProvider.showNotification(
        title: data.title ?? '',
        body: data.body ?? '',
        payload: data.parameters ?? '',
      );
    } catch (e) {
      talker.error(e.toString());
    }
  }
  // TODO: for testing
  // Exception: Test e(exception): {"senderId":null,"category":null,"collapseKey":null,"contentAvailable":false,"data":{"sound":"1","icon":"myicon","body":"抗UV吸排抗菌運動衣褲！120元起","title":"抗UV吸排抗菌運動衣褲！120元起","parameters":"{\"partner_id\":\"home\",\"action\":\"category\",\"id\":\"457\",\"type\":\"activities\"}"},"from":"498175649962","messageId":"0:1722260350805142%307e5024f9fd7ecd","messageType":null,"mutableContent":false,"notification":null,"sentTime":1722260350796,"threadId":null,"ttl":2419200}
  // FirebaseCrashlytics.instance.recordFlutterError(
  //   FlutterErrorDetails(
  //     exception: Exception("Test e(exception): $jsonString"),
  //   ),
  //   fatal: true,
  // );
}

Future<void> _initAppier() async {
  final prefProvider = Get.find<PrefProvider>();
  await AppierFlutter.configure(
    prefProvider.appierKey,
    senderId: Constants.fcmSenderId, // android sender id - optional
    appGroup: Constants.appGroup,
    isDev:
        prefProvider.isSandBox, // ios dev or prod - default `false` - optional
  );
  await AppierFlutter.setUniversalLinkDomains(Constants.domains);
  // 啟用儲存通知
  await AppierFlutter.enableStoredNotifications();
  // 設定儲存通知上限
  await AppierFlutter.setStoredNotificationsLimit(20);
}

Future<NotificationProvider> _initNotificationProvider(Talker talker) async {
  return Get.putAsync(
    () async {
      final notificationProvider = NotificationProvider(
        talker: talker,
      );
      await notificationProvider.initialize();
      return notificationProvider;
    },
    permanent: true,
  );
}

Future<void> _init() async {
  await Hive.initFlutter();
  final userDefault = await Hive.openBox(Keys.userDefault);
  Get.lazyPut(
    () => TalkerFlutter.init(
      observer: CrashlyticsTalkerObserver(),
    ),
    fenix: true,
  );

  Get.lazyPut(() => BoxProvider(userDefault), fenix: true);
  // Get.putAsync(() => PackageInfo.fromPlatform(), permanent: true);
  final packageInfo = await PackageInfo.fromPlatform();
  Get.lazyPut(() => packageInfo, fenix: true);
  Get.lazyPut(() => DeviceInfoPlugin(), fenix: true);
  Get.lazyPut(
    () => PrefProvider(
      deviceInfo: Get.find(),
      packageInfo: Get.find(),
      boxProvider: Get.find(),
      talker: Get.find(),
    ),
    fenix: true,
  );
  await _initFirebase();
  await _initAppier();
  Get.lazyPut(
    () {
      final options = BaseOptions(
        // baseUrl: "your base url",
        receiveDataWhenStatusError: true,
        connectTimeout: 1.minutes,
        receiveTimeout: 1.minutes,
      );
      final dio = Dio(options);
      // dio.interceptors.add(PrettyDioLogger());
      // customization
      // dio.interceptors.add(PrettyDioLogger(
      //   requestHeader: true,
      //   requestBody: true,
      //   responseBody: true,
      //   responseHeader: false,
      //   error: true,
      //   compact: true,
      //   maxWidth: 90,
      // ));
      dio.interceptors.add(
        TalkerDioLogger(
          talker: Get.find<Talker>(),
          settings: const TalkerDioLoggerSettings(
            printRequestHeaders: true,
            printResponseHeaders: false,
            printRequestData: true,
            printResponseData: false,
          ),
        ),
      );
      return dio;
    },
    fenix: true,
  );
  Get.lazyPut(
    () => ApiProvider(
      dio: Get.find(),
      prefProvider: Get.find(),
    ),
    fenix: true,
  );
  Get.lazyPut(
    () => AppierProvider(
      apiProvider: Get.find(),
    ),
    fenix: true,
  );
  Get.lazyPut(
    () => WabowProvider(
      apiProvider: Get.find(),
      appierProvider: Get.find(),
    ),
    fenix: true,
  );
  Get.lazyPut(
    () => MessageProvider(
      wabowProvider: Get.find(),
    ),
    fenix: true,
  );
  Get.lazyPut(
    () => AddressProvider(
      wabowProvider: Get.find(),
    ),
    fenix: true,
  );
  Get.lazyPut(
    () => BankProvider(
      wabowProvider: Get.find(),
    ),
    fenix: true,
  );
  Get.putAsync(
    () => LoveCodeProvider.init(),
    permanent: true,
  );
  Get.lazyPut(
    () => FlutterShareMe(),
    fenix: true,
  );
  await LineSDK.instance.setup(Constants.lineClientId);
  await _initNotificationProvider(Get.find());
}
